package {{configPackage}};

{{#models}}
    {{#model}}
        {{#isEnum}}
import {{modelPackage}}.{{classname}};
        {{/isEnum}}
    {{/model}}
{{/models}}

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.convert.converter.Converter;

@Configuration(value = "{{configPackage}}.enumConverterConfiguration")
public class EnumConverterConfiguration {

{{#models}}
{{#model}}
{{#isEnum}}
    @Bean(name = "{{configPackage}}.EnumConverterConfiguration.{{classVarName}}Converter")
    Converter<{{{dataType}}}, {{classname}}> {{classVarName}}Converter() {
        return new Converter<{{{dataType}}}, {{classname}}>() {
            @Override
            public {{classname}} convert({{{dataType}}} source) {
                return {{classname}}.fromValue(source);
            }
        };
    }
{{/isEnum}}
{{/model}}
{{/models}}

}
