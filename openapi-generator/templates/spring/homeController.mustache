package {{configPackage}};

import org.springframework.context.annotation.Bean;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
{{#sourceDocumentationProvider}}
import com.fasterxml.jackson.dataformat.yaml.YAMLMapper;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.util.StreamUtils;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.GetMapping;
{{/sourceDocumentationProvider}}
{{#useSwaggerUI}}
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.GetMapping;
{{/useSwaggerUI}}
{{#reactive}}
import org.springframework.web.reactive.function.server.RouterFunction;
import org.springframework.web.reactive.function.server.ServerResponse;
{{/reactive}}
{{#sourceDocumentationProvider}}
import java.io.IOException;
import java.io.InputStream;
{{/sourceDocumentationProvider}}
{{#reactive}}
import java.net.URI;
{{/reactive}}
{{#sourceDocumentationProvider}}
import java.nio.charset.Charset;
{{/sourceDocumentationProvider}}
{{#reactive}}

import static org.springframework.web.reactive.function.server.RequestPredicates.GET;
import static org.springframework.web.reactive.function.server.RouterFunctions.route;
{{/reactive}}

/**
 * Home redirection to OpenAPI api documentation
 */
@Controller
public class HomeController {
{{#sourceDocumentationProvider}}

    private static YAMLMapper yamlMapper = new YAMLMapper();

    @Value("classpath:/openapi.yaml")
    private Resource openapi;

    @Bean
    public String openapiContent() throws IOException {
        try(InputStream is = openapi.getInputStream()) {
            return StreamUtils.copyToString(is, Charset.defaultCharset());
        }
    }

    @GetMapping(value = "/openapi.yaml", produces = "application/vnd.oai.openapi")
    @ResponseBody
    public String openapiYaml() throws IOException {
        return openapiContent();
    }

    @GetMapping(value = "/openapi.json", produces = "application/json")
    @ResponseBody
    public Object openapiJson() throws IOException {
        return yamlMapper.readValue(openapiContent(), Object.class);
    }
{{/sourceDocumentationProvider}}
{{#useSwaggerUI}}
{{^springDocDocumentationProvider}}

{{#sourceDocumentationProvider}}
    static final String API_DOCS_PATH = "/openapi.json";
{{/sourceDocumentationProvider}}
{{#springFoxDocumentationProvider}}
    static final String API_DOCS_PATH = "/v2/api-docs";
{{/springFoxDocumentationProvider}}

    @GetMapping(value = "/swagger-config.yaml", produces = "text/plain")
    @ResponseBody
    public String swaggerConfig() {
        return "url: " + API_DOCS_PATH + "\n";
    }
{{/springDocDocumentationProvider}}
{{#reactive}}

    @Bean
    RouterFunction<ServerResponse> index() {
        return route(
            GET("/"),
            req -> ServerResponse.temporaryRedirect(URI.create("swagger-ui.html")).build()
        );
    }
{{/reactive}}
{{^reactive}}

    @RequestMapping("/")
    public String index() {
        return "redirect:swagger-ui.html";
    }
{{/reactive}}
{{/useSwaggerUI}}

}