
  public static class Builder {{#parentModel}}extends {{classname}}.Builder {{/parentModel}}{

    private {{classname}} instance;

    public Builder() {
      this(new {{classname}}());
    }

    protected Builder({{classname}} instance) {
    {{#parentModel}}
      super(instance); // the parent builder shares the same instance
    {{/parentModel}}
      this.instance = instance;
    }

    protected Builder copyOf({{classname}} value) { {{#parentModel}}
      super.copyOf(value);{{/parentModel}}{{#vars}}
      this.instance.{{setter}}(value.{{name}});{{/vars}}
      return this;
    }

{{#vars}}
    {{#deprecated}}
    @Deprecated
    {{/deprecated}}
    public {{classname}}.Builder {{name}}({{#removeAnnotations}}{{{datatypeWithEnum}}}{{/removeAnnotations}} {{name}}) {
      this.instance.{{name}}({{name}});
      return this;
    }
    {{#openApiNullable}}{{#isNullable}}
    public {{classname}}.Builder {{name}}(JsonNullable<{{#removeAnnotations}}{{{datatypeWithEnum}}}{{/removeAnnotations}}> {{name}}) {
      this.instance.{{name}} = {{name}};
      return this;
    }
    {{/isNullable}}{{/openApiNullable}}
{{/vars}}
{{#parentVars}}
    @Override
    public {{classname}}.Builder {{name}}({{#removeAnnotations}}{{{datatypeWithEnum}}}{{/removeAnnotations}} {{name}}) {
      this.instance.{{name}}({{name}});
      return this;
    }
    {{#openApiNullable}}{{#isNullable}}
    public {{classname}}.Builder {{name}}(JsonNullable<{{#removeAnnotations}}{{{datatypeWithEnum}}}{{/removeAnnotations}}> {{name}}) {
      this.instance.{{setter}}({{name}});
      return this;
    }
    {{/isNullable}}{{/openApiNullable}}
{{/parentVars}}
{{#additionalPropertiesType}}
    public {{classname}}.Builder additionalProperties(Map<String, {{{.}}}> additionalProperties) {
      this.instance.additionalProperties = additionalProperties;
      return this;
    }

{{/additionalPropertiesType}}
    /**
    * returns a built {{classname}} instance.
    *
    * The builder is not reusable (NullPointerException)
    */
    public {{classname}} build() {
      try {
        return this.instance;
      } finally {
        // ensure that this.instance is not reused{{#parentModel}}
        super.build();{{/parentModel}}
        this.instance = null;
      }
    }

    @Override
    public String toString() {
      return getClass() + "=(" + instance + ")";
    }
  }

  /**
  * Create a builder with no initialized field (except for the default values).
  */
  public static {{classname}}.Builder builder() {
    return new {{classname}}.Builder();
  }

  /**
  * Create a builder with a shallow copy of this instance.
  */
  public {{classname}}.Builder toBuilder() {
    {{! using the instance setter ensure compatibility with Optional and JsonNullable}}
    {{classname}}.Builder builder = new {{classname}}.Builder();
    return builder.copyOf(this);
  }
