{{>additionalOneOfTypeAnnotations}}
{{#withXml}}
{{>xmlAnnotation}}
{{/withXml}}
{{#discriminator}}
{{>typeInfoAnnotation}}
{{/discriminator}}

public {{>sealed}}interface {{classname}}{{#vendorExtensions.x-implements}}{{#-first}} extends {{{.}}}{{/-first}}{{^-first}}, {{{.}}}{{/-first}}{{/vendorExtensions.x-implements}} {{>permits}}{
    {{#discriminator}}
    public {{propertyType}} {{propertyGetter}}();
    {{/discriminator}}
}