package {{package}};

{{#imports}}import {{import}};
{{/imports}}

{{#_api_controller_impl_}}
{{#swagger2AnnotationLibrary}}
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;
{{/swagger2AnnotationLibrary}}
{{#swagger1AnnotationLibrary}}
import io.swagger.annotations.*;
{{/swagger1AnnotationLibrary}}
{{/_api_controller_impl_}}

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
{{#useResponseEntity}}
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
{{/useResponseEntity}}
{{^useResponseEntity}}
import org.springframework.web.bind.annotation.RestController;
{{/useResponseEntity}}
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.CookieValue;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestPart;
import org.springframework.web.multipart.MultipartFile;
{{^isDelegate}}
import org.springframework.web.context.request.NativeWebRequest;
{{/isDelegate}}

{{#useBeanValidation}}
import {{javaxPackage}}.validation.constraints.*;
import {{javaxPackage}}.validation.Valid;
{{/useBeanValidation}}

import java.util.List;
import java.util.Map;
import java.util.Optional;



{{#useResponseEntity}}
@Controller
{{/useResponseEntity}}
{{^useResponseEntity}}
@RestController
{{/useResponseEntity}}
{{#useRequestMappingOnController}}
{{=<% %>=}}
@RequestMapping("${openapi.<%title%>.base-path:<%>defaultBasePath%>}")
<%={{ }}=%>
{{/useRequestMappingOnController}}
{{#operations}}
public class {{classname}}Controller implements {{classname}} {
{{#isDelegate}}

    private final {{classname}}Delegate delegate;

    public {{classname}}Controller(@Autowired(required = false) {{classname}}Delegate delegate) {
        this.delegate = Optional.ofNullable(delegate).orElse(new {{classname}}Delegate() {});
    }

    @Override
    public {{classname}}Delegate getDelegate() {
        return delegate;
    }
{{/isDelegate}}
{{^isDelegate}}
    {{^reactive}}

    private final NativeWebRequest request;

    @Autowired
    public {{classname}}Controller(NativeWebRequest request) {
        this.request = request;
    }

    @Override
    public Optional<NativeWebRequest> getRequest() {
        return Optional.ofNullable(request);
    }
    {{/reactive}}
{{/isDelegate}}

{{#_api_controller_impl_}}
{{#operation}}
    /**
     * {{httpMethod}} {{{path}}}{{#summary}} : {{.}}{{/summary}}
    {{#notes}}
     * {{.}}
    {{/notes}}
     *
    {{#allParams}}
     * @param {{paramName}} {{description}}{{#required}} (required){{/required}}{{^required}} (optional{{#defaultValue}}, default to {{.}}{{/defaultValue}}){{/required}}
    {{/allParams}}
     * @return {{#responses}}{{message}} (status code {{code}}){{^-last}}
     *         or {{/-last}}{{/responses}}
    {{#isDeprecated}}
     * @deprecated
    {{/isDeprecated}}
    {{#externalDocs}}
     * {{description}}
     * @see <a href="{{url}}">{{summary}} Documentation</a>
    {{/externalDocs}}
     * @see {{classname}}#{{operationId}}
     */
    {{#isDeprecated}}
    @Deprecated
    {{/isDeprecated}}
    public {{#responseWrapper}}{{.}}<{{/responseWrapper}}{{#useResponseEntity}}ResponseEntity<{{/useResponseEntity}}{{>returnTypes}}{{#useResponseEntity}}>{{/useResponseEntity}}{{#responseWrapper}}>{{/responseWrapper}} {{operationId}}(
        {{#allParams}}{{>queryParams}}{{>pathParams}}{{>headerParams}}{{>bodyParams}}{{>formParams}}{{>cookieParams}}{{^-last}},
        {{/-last}}{{/allParams}}{{#vendorExtensions.x-spring-paginated}}{{#hasParams}},
        {{/hasParams}}{{^hasParams}}{{#reactive}},{{/reactive}}{{/hasParams}}{{#springFoxDocumentationProvider}}@ApiIgnore {{/springFoxDocumentationProvider}}{{#springDocDocumentationProvider}}@ParameterObject {{/springDocDocumentationProvider}}final Pageable pageable{{/vendorExtensions.x-spring-paginated}}
    ) {
    {{^isDelegate}}
        {{^async}}
        {{>methodBody}}
        {{/async}}
        {{#async}}
        return new Callable<{{#useResponseEntity}}ResponseEntity<{{/useResponseEntity}}{{>returnTypes}}{{#useResponseEntity}}>{{/useResponseEntity}}>() {
            @Override
            public {{#useResponseEntity}}ResponseEntity<{{/useResponseEntity}}{{>returnTypes}}{{#useResponseEntity}}>{{/useResponseEntity}} call() {
                {{>methodBody}}
            }
        };
        {{/async}}
    {{/isDelegate}}
    {{#isDelegate}}
        {{^isVoid}}return {{/isVoid}}{{#isVoid}}{{#useResponseEntity}}return {{/useResponseEntity}}{{^useResponseEntity}}{{#reactive}}return {{/reactive}}{{/useResponseEntity}}{{/isVoid}}delegate.{{operationId}}({{#allParams}}{{paramName}}{{^-last}}, {{/-last}}{{/allParams}}{{#vendorExtensions.x-spring-paginated}}{{#hasParams}}, {{/hasParams}}{{^hasParams}}{{#reactive}}, {{/reactive}}{{/hasParams}}pageable{{/vendorExtensions.x-spring-paginated}});
    {{/isDelegate}}
    }

{{/operation}}
{{/_api_controller_impl_}}
}
{{/operations}}
