{{#swagger2AnnotationLibrary}}@Parameter(name = "{{{baseName}}}"{{#isDeprecated}}, deprecated = true{{/isDeprecated}}, description = "{{{description}}}"{{#required}}, required = true{{/required}}{{#isPathParam}}, in = ParameterIn.PATH{{/isPathParam}}{{#isQueryParam}}, in = ParameterIn.QUERY{{/isQueryParam}}{{#isCookieParam}}, in = ParameterIn.COOKIE{{/isCookieParam}}{{#isHeaderParam}}, in = ParameterIn.HEADER{{/isHeaderParam}}){{/swagger2AnnotationLibrary}}{{#swagger1AnnotationLibrary}}@ApiParam(value = "{{{description}}}"{{#required}}, required = true{{/required}}{{#allowableValues}}, {{> allowableValues }}{{/allowableValues}}{{#defaultValue}}, defaultValue = "{{{.}}}"{{/defaultValue}}){{/swagger1AnnotationLibrary}}