package {{configPackage}};

import com.fasterxml.jackson.core.JsonParser;
import com.fasterxml.jackson.databind.DeserializationContext;
import com.fasterxml.jackson.databind.JsonDeserializer;
import com.fasterxml.jackson.databind.JsonNode;

import java.io.IOException;

public class MongoDecimalDeserializer extends JsonDeserializer<Double> {
    @Override
    public Double deserialize(JsonParser p, DeserializationContext ctxt) throws IOException {
        JsonNode node = p.getCodec().readTree(p);

        // Handle simple number
        if (node.isNumber()) {
        return node.asDouble();
        }

        // Handle MongoDB decimal object
        if (node.isObject() && node.has("$numberDecimal")) {
        return Double.parseDouble(node.get("$numberDecimal").asText());
        }

        return null;
    }
}