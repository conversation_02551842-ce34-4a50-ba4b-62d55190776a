package {{apiPackage}};

{{#reactive}}
import java.nio.charset.StandardCharsets;
import org.springframework.core.io.buffer.DefaultDataBuffer;
import org.springframework.core.io.buffer.DefaultDataBufferFactory;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
{{/reactive}}
{{^reactive}}
import org.springframework.web.context.request.NativeWebRequest;

import {{javaxPackage}}.servlet.http.HttpServletResponse;
import java.io.IOException;
{{/reactive}}

public class ApiUtil {
{{^reactive}}
    public static void setExampleResponse(NativeWebRequest req, String contentType, String example) {
        try {
            HttpServletResponse res = req.getNativeResponse(HttpServletResponse.class);
            res.setCharacterEncoding("UTF-8");
            res.addHeader("Content-Type", contentType);
            res.getWriter().print(example);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
{{/reactive}}
{{#reactive}}
    public static Mono<Void> getExampleResponse(ServerWebExchange exchange, MediaType mediaType, String example) {
        ServerHttpResponse response = exchange.getResponse();
        response.getHeaders().setContentType(mediaType);

        byte[] exampleBytes = example.getBytes(StandardCharsets.UTF_8);
        DefaultDataBuffer data = new DefaultDataBufferFactory().wrap(exampleBytes);
        return response.writeWith(Mono.just(data));
    }
{{/reactive}}
}
