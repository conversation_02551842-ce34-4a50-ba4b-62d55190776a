/**
 * {{description}}{{^description}}{{classname}}{{/description}}{{#isDeprecated}}
 * @deprecated{{/isDeprecated}}
 */
{{>additionalModelTypeAnnotations}}
{{#isDeprecated}}
@Deprecated
{{/isDeprecated}}
{{#description}}
{{#swagger1AnnotationLibrary}}
@ApiModel(description = "{{{description}}}")
{{/swagger1AnnotationLibrary}}
{{#swagger2AnnotationLibrary}}
@Schema({{#name}}name = "{{name}}", {{/name}}description = "{{{description}}}"{{#deprecated}}, deprecated = true{{/deprecated}})
{{/swagger2AnnotationLibrary}}
{{/description}}
{{#discriminator}}
{{>typeInfoAnnotation}}
{{/discriminator}}
{{#jackson}}
{{#isClassnameSanitized}}
{{^hasDiscriminatorWithNonEmptyMapping}}
@JsonTypeName("{{name}}")
{{/hasDiscriminatorWithNonEmptyMapping}}
{{/isClassnameSanitized}}
{{/jackson}}
{{#withXml}}
{{>xmlAnnotation}}
{{/withXml}}
{{#vendorExtensions.x-class-extra-annotation}}
{{{vendorExtensions.x-class-extra-annotation}}}
{{/vendorExtensions.x-class-extra-annotation}}
public {{>sealed}}class {{classname}}{{#vendorExtensions.x-is-response-generic}}<{{vendorExtensions.x-response-generic-type}}>{{/vendorExtensions.x-is-response-generic}}{{#parent}} extends {{{parent}}}{{/parent}}{{^parent}}{{#hateoas}} extends RepresentationModel<{{classname}}> {{/hateoas}}{{/parent}}{{#vendorExtensions.x-implements}}{{#-first}} implements {{{.}}}{{/-first}}{{^-first}}, {{{.}}}{{/-first}}{{/vendorExtensions.x-implements}} {{>permits}}{
{{#serializableModel}}

  private static final long serialVersionUID = 1L;
{{/serializableModel}}
  {{#vars}}

    {{#isEnum}}
    {{^isContainer}}
{{>enumClass}}
    {{/isContainer}}
    {{#isContainer}}
    {{#mostInnerItems}}
{{>enumClass}}
    {{/mostInnerItems}}
    {{/isContainer}}
    {{/isEnum}}
  {{#gson}}
  @SerializedName("{{baseName}}")
  {{/gson}}
  {{#lombok.RequiredArgsConstructor}}
  {{^useBeanValidation}}
  {{#required}}
  @lombok.NonNull
  {{/required}}
  {{/useBeanValidation}}
  {{/lombok.RequiredArgsConstructor}}
  {{#lombok.ToString}}
  {{#isPassword}}
  @lombok.ToString.Exclude
  {{/isPassword}}
  {{/lombok.ToString}}
  {{#vendorExtensions.x-field-extra-annotation}}
  {{{vendorExtensions.x-field-extra-annotation}}}
  {{/vendorExtensions.x-field-extra-annotation}}
  {{#lombok.Builder}}
  {{#defaultValue}}
  @lombok.Builder.Default
  {{/defaultValue}}
  {{/lombok.Builder}}
  {{#deprecated}}
  @Deprecated
  {{/deprecated}}
  {{#isContainer}}
  {{#useBeanValidation}}@Valid{{/useBeanValidation}}
  {{#openApiNullable}}
  private {{>nullableAnnotation}}{{#isNullable}}{{>nullableDataTypeBeanValidation}} {{name}} = JsonNullable.<{{{datatypeWithEnum}}}>undefined();{{/isNullable}}{{^required}}{{^isNullable}}{{>nullableDataTypeBeanValidation}} {{name}}{{#defaultValue}} = {{{.}}}{{/defaultValue}};{{/isNullable}}{{/required}}{{#required}}{{^isNullable}}{{>nullableDataTypeBeanValidation}} {{name}}{{#defaultValue}} = {{{.}}}{{/defaultValue}};{{/isNullable}}{{/required}}
  {{/openApiNullable}}
  {{^openApiNullable}}
  private {{>nullableAnnotation}}{{>nullableDataType}} {{name}}{{#defaultValue}} = {{{.}}}{{/defaultValue}};
  {{/openApiNullable}}
  {{/isContainer}}
  {{^isContainer}}
  {{#isDate}}
  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
  {{/isDate}}
  {{#isDateTime}}
  @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
  {{/isDateTime}}
  {{#openApiNullable}}
  private {{>nullableAnnotation}}{{#isNullable}}{{>nullableDataTypeBeanValidation}} {{name}} = JsonNullable.<{{{datatypeWithEnum}}}>undefined();{{/isNullable}}{{^required}}{{^isNullable}}{{>nullableDataTypeBeanValidation}} {{name}}{{#useOptional}} = Optional.{{^defaultValue}}empty(){{/defaultValue}}{{#defaultValue}}of({{{.}}}){{/defaultValue}};{{/useOptional}}{{^useOptional}}{{#defaultValue}} = {{{.}}}{{/defaultValue}};{{/useOptional}}{{/isNullable}}{{/required}}{{^isNullable}}{{#required}}{{>nullableDataTypeBeanValidation}} {{name}}{{#defaultValue}} = {{{.}}}{{/defaultValue}};{{/required}}{{/isNullable}}
  {{/openApiNullable}}
  {{^openApiNullable}}
  private {{>nullableAnnotation}}{{>nullableDataType}} {{name}}{{#isNullable}} = null{{/isNullable}}{{^isNullable}}{{#defaultValue}} = {{{.}}}{{/defaultValue}}{{/isNullable}};
  {{/openApiNullable}}
  {{/isContainer}}
  {{/vars}}
  {{#vendorExtensions.x-java-no-args-constructor}}

  public {{classname}}() {
    super();
  }
  {{/vendorExtensions.x-java-no-args-constructor}}
  {{^lombok.Data}}
  {{^lombok.RequiredArgsConstructor}}
  {{#generatedConstructorWithRequiredArgs}}
  {{#hasRequired}}

  /**
   * Constructor with only required parameters{{#generateConstructorWithAllArgs}}{{^vendorExtensions.x-java-all-args-constructor}} and all parameters{{/vendorExtensions.x-java-all-args-constructor}}{{/generateConstructorWithAllArgs}}
   */
  public {{classname}}({{#requiredVars}}{{{datatypeWithEnum}}} {{name}}{{^-last}}, {{/-last}}{{/requiredVars}}) {
    {{#parent}}
    super({{#parentRequiredVars}}{{name}}{{^-last}}, {{/-last}}{{/parentRequiredVars}});
    {{/parent}}
    {{#vars}}
    {{#required}}
    {{#openApiNullable}}
    this.{{name}} = {{#isNullable}}JsonNullable.of({{/isNullable}}{{#useOptional}}{{^required}}{{^isNullable}}{{^isContainer}}Optional.ofNullable({{/isContainer}}{{/isNullable}}{{/required}}{{/useOptional}}{{name}}{{#isNullable}}){{/isNullable}}{{#useOptional}}{{^required}}{{^isNullable}}{{^isContainer}}){{/isContainer}}{{/isNullable}}{{/required}}{{/useOptional}};
    {{/openApiNullable}}
    {{^openApiNullable}}
    this.{{name}} = {{name}};
    {{/openApiNullable}}
    {{/required}}
    {{/vars}}
  }
  {{/hasRequired}}
  {{/generatedConstructorWithRequiredArgs}}
  {{/lombok.RequiredArgsConstructor}}
  {{#vendorExtensions.x-java-all-args-constructor}}

  /**
   * Constructor with all args parameters
   */
  public {{classname}}({{#vendorExtensions.x-java-all-args-constructor-vars}}{{>nullableAnnotation}}{{{datatypeWithEnum}}} {{name}}{{^-last}}, {{/-last}}{{/vendorExtensions.x-java-all-args-constructor-vars}}) {
  {{#parent}}
      super({{#parentVars}}{{name}}{{^-last}}, {{/-last}}{{/parentVars}});
  {{/parent}}
  {{#vars}}
  {{#openApiNullable}}
      this.{{name}} = {{#isNullable}}JsonNullable.of({{/isNullable}}{{#useOptional}}{{^required}}{{^isNullable}}{{^isContainer}}Optional.ofNullable({{/isContainer}}{{/isNullable}}{{/required}}{{/useOptional}}{{name}}{{#isNullable}}){{/isNullable}}{{#useOptional}}{{^required}}{{^isNullable}}{{^isContainer}}){{/isContainer}}{{/isNullable}}{{/required}}{{/useOptional}};
  {{/openApiNullable}}
  {{^openApiNullable}}
      this.{{name}} = {{name}};
  {{/openApiNullable}}
  {{/vars}}
  }
  {{/vendorExtensions.x-java-all-args-constructor}}
  {{/lombok.Data}}
  {{#vars}}
  {{^lombok.Data}}

  {{! begin feature: fluent setter methods }}
  public {{classname}}{{#vendorExtensions.x-is-response-generic}}<{{vendorExtensions.x-response-generic-type}}>{{/vendorExtensions.x-is-response-generic}} {{name}}({{>nullableAnnotation}}{{{datatypeWithEnum}}} {{name}}) {
      {{#openApiNullable}}
    this.{{name}} = {{#isNullable}}JsonNullable.of({{/isNullable}}{{#useOptional}}{{^required}}{{^isNullable}}{{^isContainer}}Optional.of{{#optionalAcceptNullable}}Nullable{{/optionalAcceptNullable}}({{/isContainer}}{{/isNullable}}{{/required}}{{/useOptional}}{{name}}{{#isNullable}}){{/isNullable}}{{#useOptional}}{{^required}}{{^isNullable}}{{^isContainer}}){{/isContainer}}{{/isNullable}}{{/required}}{{/useOptional}};
    {{/openApiNullable}}
    {{^openApiNullable}}
    this.{{name}} = {{name}};
    {{/openApiNullable}}
    return this;
  }
  {{#isArray}}

  public {{classname}}{{#vendorExtensions.x-is-response-generic}}<{{vendorExtensions.x-response-generic-type}}>{{/vendorExtensions.x-is-response-generic}} add{{nameInPascalCase}}Item({{{items.datatypeWithEnum}}} {{name}}Item) {
    {{#openApiNullable}}
    if (this.{{name}} == null{{#isNullable}} || !this.{{name}}.isPresent(){{/isNullable}}) {
      this.{{name}} = {{#isNullable}}JsonNullable.of({{/isNullable}}{{{defaultValue}}}{{^defaultValue}}new {{#uniqueItems}}LinkedHashSet{{/uniqueItems}}{{^uniqueItems}}ArrayList{{/uniqueItems}}<>(){{/defaultValue}}{{#isNullable}}){{/isNullable}};
    }
    this.{{name}}{{#isNullable}}.get(){{/isNullable}}.add({{name}}Item);
    {{/openApiNullable}}
    {{^openApiNullable}}
    if (this.{{name}} == null) {
      this.{{name}} = {{{defaultValue}}}{{^defaultValue}}new {{#uniqueItems}}LinkedHashSet{{/uniqueItems}}{{^uniqueItems}}ArrayList{{/uniqueItems}}<>(){{/defaultValue}};
    }
    this.{{name}}.add({{name}}Item);
    {{/openApiNullable}}
    return this;
  }
  {{/isArray}}
  {{#isMap}}

  public {{classname}}{{#vendorExtensions.x-is-response-generic}}<{{vendorExtensions.x-response-generic-type}}>{{/vendorExtensions.x-is-response-generic}} put{{nameInPascalCase}}Item(String key, {{{items.datatypeWithEnum}}} {{name}}Item) {
    {{#openApiNullable}}
    if (this.{{name}} == null{{#isNullable}} || !this.{{name}}.isPresent(){{/isNullable}}) {
      this.{{name}} = {{#isNullable}}JsonNullable.of({{/isNullable}}{{{defaultValue}}}{{^defaultValue}}new {{#uniqueItems}}LinkedHashSet{{/uniqueItems}}{{^uniqueItems}}HashMap{{/uniqueItems}}<>(){{/defaultValue}}{{#isNullable}}){{/isNullable}};
    }
    this.{{name}}{{#isNullable}}.get(){{/isNullable}}.put(key, {{name}}Item);
    {{/openApiNullable}}
    {{^openApiNullable}}
    if (this.{{name}} == null) {
      this.{{name}} = {{{defaultValue}}}{{^defaultValue}}new {{#uniqueItems}}LinkedHashSet{{/uniqueItems}}{{^uniqueItems}}HashMap{{/uniqueItems}}<>(){{/defaultValue}};
    }
    this.{{name}}.put(key, {{name}}Item);
    {{/openApiNullable}}
    return this;
  }
  {{/isMap}}
  {{! end feature: fluent setter methods }}
  {{! begin feature: getter and setter }}
  {{^lombok.Getter}}

  /**
  {{#description}}
   * {{{.}}}
  {{/description}}
  {{^description}}
   * Get {{name}}
  {{/description}}
  {{#minimum}}
   * minimum: {{.}}
  {{/minimum}}
  {{#maximum}}
   * maximum: {{.}}
  {{/maximum}}
   * @return {{name}}
  {{#deprecated}}
   * @deprecated
  {{/deprecated}}
   */
  {{#vendorExtensions.x-extra-annotation}}
  {{{vendorExtensions.x-extra-annotation}}}
  {{/vendorExtensions.x-extra-annotation}}
  {{#useBeanValidation}}
  {{>beanValidation}}
  {{/useBeanValidation}}
  {{#swagger2AnnotationLibrary}}
  @Schema(name = "{{{baseName}}}"{{#isReadOnly}}, accessMode = Schema.AccessMode.READ_ONLY{{/isReadOnly}}{{#example}}, example = "{{{.}}}"{{/example}}{{#description}}, description = "{{{.}}}"{{/description}}{{#deprecated}}, deprecated = true{{/deprecated}}, requiredMode = {{#required}}Schema.RequiredMode.REQUIRED{{/required}}{{^required}}Schema.RequiredMode.NOT_REQUIRED{{/required}})
  {{/swagger2AnnotationLibrary}}
  {{#swagger1AnnotationLibrary}}
  @ApiModelProperty({{#example}}example = "{{{.}}}", {{/example}}{{#required}}required = {{required}}, {{/required}}{{#isReadOnly}}readOnly = {{{isReadOnly}}}, {{/isReadOnly}}value = "{{{description}}}")
  {{/swagger1AnnotationLibrary}}
  {{#jackson}}
  @JsonProperty("{{baseName}}")
  {{#withXml}}
  @JacksonXmlProperty(localName = "{{items.xmlName}}{{^items.xmlName}}{{xmlName}}{{^xmlName}}{{baseName}}{{/xmlName}}{{/items.xmlName}}"{{#isXmlAttribute}}, isAttribute = true{{/isXmlAttribute}}{{#xmlNamespace}}, namespace = "{{.}}"{{/xmlNamespace}})
    {{#isContainer}}
  @JacksonXmlElementWrapper({{#isXmlWrapped}}localName = "{{xmlName}}{{^xmlName}}{{baseName}}{{/xmlName}}", {{#xmlNamespace}}namespace = "{{.}}", {{/xmlNamespace}}{{/isXmlWrapped}}useWrapping = {{isXmlWrapped}})
    {{/isContainer}}
  {{/withXml}}
  {{/jackson}}
  {{#withXml}}
  @Xml{{#isXmlAttribute}}Attribute{{/isXmlAttribute}}{{^isXmlAttribute}}Element{{/isXmlAttribute}}(name = "{{items.xmlName}}{{^items.xmlName}}{{xmlName}}{{^xmlName}}{{baseName}}{{/xmlName}}{{/items.xmlName}}"{{#xmlNamespace}}, namespace = "{{.}}"{{/xmlNamespace}})
    {{#isXmlWrapped}}
  @XmlElementWrapper(name = "{{xmlName}}{{^xmlName}}{{baseName}}{{/xmlName}}"{{#xmlNamespace}}, namespace = "{{.}}"{{/xmlNamespace}})
    {{/isXmlWrapped}}
  {{/withXml}}
  {{#deprecated}}
  @Deprecated
  {{/deprecated}}
  public {{>nullableDataTypeBeanValidation}} {{getter}}() {
    return {{name}};
  }
  {{/lombok.Getter}}

  {{^lombok.Setter}}
  {{#deprecated}}
  /**
   * @deprecated
   */
  {{/deprecated}}
  {{#vendorExtensions.x-setter-extra-annotation}}
  {{{vendorExtensions.x-setter-extra-annotation}}}
  {{/vendorExtensions.x-setter-extra-annotation}}
  {{#deprecated}}
  @Deprecated
  {{/deprecated}}
  public void {{setter}}({{>nullableDataType}} {{name}}) {
    this.{{name}} = {{name}};
  }
  {{/lombok.Setter}}
  {{/lombok.Data}}
  {{! end feature: getter and setter }}
  {{/vars}}
{{>additionalProperties}}
  {{^lombok.Data}}
  {{#parentVars}}

  {{^lombok.Setter}}
  {{! begin feature: fluent setter methods for inherited properties }}
  public {{classname}} {{name}}({{{datatypeWithEnum}}} {{name}}) {
    super.{{name}}({{name}});
    return this;
  }
  {{#isArray}}

  public {{classname}} add{{nameInPascalCase}}Item({{{items.datatypeWithEnum}}} {{name}}Item) {
    super.add{{nameInPascalCase}}Item({{name}}Item);
    return this;
  }
  {{/isArray}}
  {{#isMap}}

  public {{classname}} put{{nameInPascalCase}}Item(String key, {{{items.datatypeWithEnum}}} {{name}}Item) {
    super.put{{nameInPascalCase}}Item(key, {{name}}Item);
    return this;
  }
  {{/isMap}}
  {{/lombok.Setter}}
  {{! end feature: fluent setter methods for inherited properties }}
  {{/parentVars}}
  {{^lombok.EqualsAndHashCode}}
  @Override
  public boolean equals(Object o) {
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }{{#hasVars}}
    {{classname}} {{classVarName}} = ({{classname}}) o;
    return {{#vars}}{{#vendorExtensions.x-is-jackson-optional-nullable}}equalsNullable(this.{{name}}, {{classVarName}}.{{name}}){{/vendorExtensions.x-is-jackson-optional-nullable}}{{^vendorExtensions.x-is-jackson-optional-nullable}}{{#isByteArray}}Arrays{{/isByteArray}}{{^isByteArray}}Objects{{/isByteArray}}.equals(this.{{name}}, {{classVarName}}.{{name}}){{/vendorExtensions.x-is-jackson-optional-nullable}}{{^-last}} &&
        {{/-last}}{{/vars}}{{#additionalPropertiesType}} &&
    Objects.equals(this.additionalProperties, {{classVarName}}.additionalProperties){{/additionalPropertiesType}}{{#parent}} &&
        super.equals(o){{/parent}};{{/hasVars}}{{^hasVars}}
    return {{#parent}}super.equals(o){{/parent}}{{^parent}}true{{/parent}};{{/hasVars}}
  }{{#vendorExtensions.x-jackson-optional-nullable-helpers}}

  private static <T> boolean equalsNullable(JsonNullable<T> a, JsonNullable<T> b) {
    return a == b || (a != null && b != null && a.isPresent() && b.isPresent() && Objects.deepEquals(a.get(), b.get()));
  }{{/vendorExtensions.x-jackson-optional-nullable-helpers}}

  @Override
  public int hashCode() {
    return Objects.hash({{#vars}}{{#vendorExtensions.x-is-jackson-optional-nullable}}hashCodeNullable({{name}}){{/vendorExtensions.x-is-jackson-optional-nullable}}{{^vendorExtensions.x-is-jackson-optional-nullable}}{{^isByteArray}}{{name}}{{/isByteArray}}{{#isByteArray}}Arrays.hashCode({{name}}){{/isByteArray}}{{/vendorExtensions.x-is-jackson-optional-nullable}}{{^-last}}, {{/-last}}{{/vars}}{{#parent}}{{#hasVars}}, {{/hasVars}}super.hashCode(){{/parent}}{{#additionalPropertiesType}}{{#hasVars}}, {{/hasVars}}{{^hasVars}}{{#parent}}, {{/parent}}{{/hasVars}}additionalProperties{{/additionalPropertiesType}});
  }{{#vendorExtensions.x-jackson-optional-nullable-helpers}}

  private static <T> int hashCodeNullable(JsonNullable<T> a) {
    if (a == null) {
      return 1;
    }
    return a.isPresent() ? Arrays.deepHashCode(new Object[]{a.get()}) : 31;
  }{{/vendorExtensions.x-jackson-optional-nullable-helpers}}
  {{/lombok.EqualsAndHashCode}}

  {{^lombok.ToString}}
  @Override
  public String toString() {
      try {
        return objectMapper.writeValueAsString(this);
      } catch (JsonProcessingException e) {
        StringBuilder sb = new StringBuilder();
        sb.append("class {{classname}} {\n");
        {{#parent}}
        sb.append("    ").append(toIndentedString(super.toString())).append("\n");
        {{/parent}}
        {{#vars}}sb.append("    {{name}}: ").append({{#isPassword}}"*"{{/isPassword}}{{^isPassword}}toIndentedString({{name}}){{/isPassword}}).append("\n");
        {{/vars}}{{#additionalPropertiesType}}
        sb.append("    additionalProperties: ").append(toIndentedString(additionalProperties)).append("\n");
        {{/additionalPropertiesType}}sb.append("}");
        return sb.toString();
      }
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
  {{/lombok.ToString}}
  {{#generateBuilders}}
  {{>javaBuilder}}
  {{/generateBuilders}}
  {{/lombok.Data}}

  private static ObjectMapper objectMapper;
  static {
      objectMapper = new ObjectMapper();
      objectMapper.enable(SerializationFeature.WRITE_ENUMS_USING_TO_STRING);
      objectMapper.enable(DeserializationFeature.READ_ENUMS_USING_TO_STRING);
      objectMapper.disable(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES);
      objectMapper.disable(DeserializationFeature.FAIL_ON_INVALID_SUBTYPE);
      objectMapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
      objectMapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
      objectMapper.setSerializationInclusion(JsonInclude.Include.NON_EMPTY);
      objectMapper.registerModule(new JavaTimeModule());
      JsonNullableModule jnm = new JsonNullableModule();
      objectMapper.registerModule(jnm);
  }
}
