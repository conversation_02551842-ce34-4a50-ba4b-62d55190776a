# This workflow will build a Java project with <PERSON><PERSON>, and cache/restore any dependencies to improve the workflow execution time
# For more information see: https://help.github.com/actions/language-and-framework-guides/building-and-testing-java-with-maven
#
# This file is auto-generated by OpenAPI Generator (https://openapi-generator.tech)

name: Java CI with Ma<PERSON>

on:
  push:
    branches: [ main, master ]
  pull_request:
    branches: [ main, master ]

jobs:
  build:
    name: Build {{{appName}}}
    runs-on: ubuntu-latest
    strategy:
      matrix:
        java: [ 17, 21 ]
    steps:
    - uses: actions/checkout@v4
    - name: Set up JDK
      uses: actions/setup-java@v4
      with:
      {{=< >=}}
        java-version: ${{ matrix.java }}
        distribution: 'temurin'
        cache: maven
    - name: Build with Maven
      run: mvn -B package --no-transfer-progress --file pom.xml
