apply plugin: 'idea'
apply plugin: 'eclipse'

group = '{{groupId}}'
version = '{{artifactVersion}}'

buildscript {
    repositories {
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:2.3.+'
        classpath 'com.github.dcendents:android-maven-gradle-plugin:1.5'
    }
}

repositories {
    mavenCentral()
}

if(hasProperty('target') && target == 'android') {

    apply plugin: 'com.android.library'
    apply plugin: 'com.github.dcendents.android-maven'

    android {
        compileSdkVersion 25
        buildToolsVersion '25.0.2'
        defaultConfig {
            minSdkVersion 14
            targetSdkVersion 25
        }

        compileOptions {
            sourceCompatibility JavaVersion.VERSION_1_8
            targetCompatibility JavaVersion.VERSION_1_8
        }

        // Rename the aar correctly
        libraryVariants.all { variant ->
            variant.outputs.each { output ->
                def outputFile = output.outputFile
                if (outputFile != null && outputFile.name.endsWith('.aar')) {
                    def fileName = "${project.name}-${variant.baseName}-${version}.aar"
                    output.outputFile = new File(outputFile.parent, fileName)
                }
            }
        }

        dependencies {
            provided "jakarta.annotation:jakarta.annotation-api:$jakarta_annotation_version"
        }
    }

    afterEvaluate {
        android.libraryVariants.all { variant ->
            def task = project.tasks.create "jar${variant.name.capitalize()}", Jar
            task.description = "Create jar artifact for ${variant.name}"
            task.dependsOn variant.javaCompile
            task.from variant.javaCompile.destinationDirectory
            task.destinationDirectory = project.file("${project.buildDir}/outputs/jar")
            task.archiveFileName = "${project.name}-${variant.baseName}-${version}.jar"
            artifacts.add('archives', task);
        }
    }

    task sourcesJar(type: Jar) {
        from android.sourceSets.main.java.srcDirs
        archiveClassifier = 'sources'
    }

    artifacts {
        archives sourcesJar
    }

} else {

    apply plugin: 'java'
    apply plugin: 'maven-publish'

    sourceCompatibility = JavaVersion.VERSION_1_8
    targetCompatibility = JavaVersion.VERSION_1_8

    publishing {
        publications {
            maven(MavenPublication) {
               artifactId = '{{artifactId}}'

               from components.java
            }
        }
    }

    task execute(type:JavaExec) {
       main = System.getProperty('mainClass')
       classpath = sourceSets.main.runtimeClasspath
    }

    task sourcesJar(type: Jar, dependsOn: classes) {
        archiveClassifier = 'sources'
        from sourceSets.main.allSource
    }

    task javadocJar(type: Jar, dependsOn: javadoc) {
        archiveClassifier = 'javadoc'
        from javadoc.destinationDir
    }

    artifacts {
        archives sourcesJar
        archives javadocJar
    }
}

ext {
    swagger_annotations_version = "1.6.3"
    jackson_version = "2.17.1"
    jackson_databind_version = "2.17.1"
    {{#openApiNullable}}
    jackson_databind_nullable_version = "0.2.6"
    {{/openApiNullable}}
    jakarta_annotation_version = "1.3.5"
    {{#useBeanValidation}}
    bean_validation_version = "3.0.2"
    {{/useBeanValidation}}
    jersey_version = "1.19.4"
    jodatime_version = "2.9.9"
    junit_version = "5.10.2"
    {{#useReflectionEqualsHashCode}}
    commons_lang3_version = "3.17.0"
    {{/useReflectionEqualsHashCode}}
}

dependencies {
    implementation "io.swagger:swagger-annotations:$swagger_annotations_version"
    implementation "com.google.code.findbugs:jsr305:3.0.2"
    implementation "com.sun.jersey:jersey-client:$jersey_version"
    implementation "com.sun.jersey.contribs:jersey-multipart:$jersey_version"
    implementation "com.fasterxml.jackson.core:jackson-core:$jackson_version"
    implementation "com.fasterxml.jackson.core:jackson-annotations:$jackson_version"
    implementation "com.fasterxml.jackson.core:jackson-databind:$jackson_databind_version"
    implementation "com.fasterxml.jackson.jaxrs:jackson-jaxrs-json-provider:$jackson_version"
    {{#openApiNullable}}
    implementation "org.openapitools:jackson-databind-nullable:$jackson_databind_nullable_version"
    {{/openApiNullable}}
    {{#joda}}
    implementation "com.fasterxml.jackson.datatype:jackson-datatype-joda:$jackson_version"
    {{/joda}}
    implementation "com.fasterxml.jackson.datatype:jackson-datatype-jsr310:$jackson_version"
    implementation "jakarta.annotation:jakarta.annotation-api:$jakarta_annotation_version"
    {{#useBeanValidation}}
    implementation "jakarta.validation:jakarta.validation-api:$bean_validation_version"
    {{/useBeanValidation}}
    {{#useReflectionEqualsHashCode}}
    implementation "org.apache.commons:commons-lang3:$commons_lang3_version"
    {{/useReflectionEqualsHashCode}}
    testImplementation "junit:junit:$junit_version"
    testImplementation "org.junit.jupiter:junit-jupiter-api:$junit_version"
    testRuntimeOnly "org.junit.jupiter:junit-jupiter-engine:$junit_version"
}

test {
    // Enable JUnit 5 (Gradle 4.6+).
    useJUnitPlatform()

    // Always run tests, even when nothing changed.
    dependsOn 'cleanTest'

    // Show test results.
    testLogging {
        events "passed", "skipped", "failed"
    }

}
