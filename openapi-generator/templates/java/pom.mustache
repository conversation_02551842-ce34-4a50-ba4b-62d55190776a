<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>{{groupId}}</groupId>
    <artifactId>{{artifactId}}</artifactId>
    <packaging>jar</packaging>
    <name>{{artifactId}}</name>
    <version>{{artifactVersion}}</version>
    <url>{{artifactUrl}}</url>
    <description>{{artifactDescription}}</description>
    <scm>
        <connection>{{scmConnection}}</connection>
        <developerConnection>{{scmDeveloperConnection}}</developerConnection>
        <url>{{scmUrl}}</url>
    </scm>
{{#parentOverridden}}
    <parent>
        <groupId>{{{parentGroupId}}}</groupId>
        <artifactId>{{{parentArtifactId}}}</artifactId>
        <version>{{{parentVersion}}}</version>
    </parent>
{{/parentOverridden}}

    <licenses>
        <license>
            <name>{{licenseName}}</name>
            <url>{{licenseUrl}}</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <developers>
        <developer>
            <name>{{developerName}}</name>
            <email>{{developerEmail}}</email>
            <organization>{{developerOrganization}}</organization>
            <organizationUrl>{{developerOrganizationUrl}}</organizationUrl>
        </developer>
    </developers>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>{{#javaVersion}}{{javaVersion}}{{/javaVersion}}{{^javaVersion}}17{{/javaVersion}}</source>
                    <target>{{#javaVersion}}{{javaVersion}}{{/javaVersion}}{{^javaVersion}}17{{/javaVersion}}</target>
                    <fork>true</fork>
                    <meminitial>128m</meminitial>
                    <maxmem>512m</maxmem>
                    <compilerArgs>
                        <arg>-Xlint:all</arg>
                        <arg>-J-Xss4m</arg><!-- Compiling the generated JSON.java file may require larger stack size. -->
                    </compilerArgs>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>3.0.0-M1</version>
                <executions>
                    <execution>
                        <id>enforce-maven</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireMavenVersion>
                                    <version>2.2.0</version>
                                </requireMavenVersion>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.12</version>
                <configuration>
                    <systemPropertyVariables>
                        <property>
                            <name>loggerPath</name>
                            <value>conf/log4j.properties</value>
                        </property>
                    </systemPropertyVariables>
                    <argLine>-Xms512m -Xmx1500m</argLine>
                    <parallel>methods</parallel>
                    <forkMode>pertest</forkMode>
                </configuration>
                <dependencies>
                    <!--Custom provider and engine for Junit 5 to surefire-->
                    <dependency>
                        <groupId>org.junit.jupiter</groupId>
                        <artifactId>junit-jupiter-engine</artifactId>
                        <version>${junit-version}</version>
                    </dependency>
                </dependencies>
            </plugin>
            <!-- maven-dependency-plugin removed - dependencies are now included in the fat JAR via shade plugin -->

            <!-- Create fat JAR with all dependencies -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-shade-plugin</artifactId>
                <version>3.4.1</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>shade</goal>
                        </goals>
                        <configuration>
                            <finalName>{{artifactId}}</finalName>
                            <createDependencyReducedPom>false</createDependencyReducedPom>
                            <transformers>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.ManifestResourceTransformer">
                                    <manifestEntries>
                                        <Built-By>OpenAPI Generator</Built-By>
                                        <Implementation-Title>{{artifactId}}</Implementation-Title>
                                        <Implementation-Version>{{artifactVersion}}</Implementation-Version>
                                    </manifestEntries>
                                </transformer>
                                <transformer implementation="org.apache.maven.plugins.shade.resource.ServicesResourceTransformer"/>
                            </transformers>
                            <filters>
                                <filter>
                                    <artifact>*:*</artifact>
                                    <excludes>
                                        <exclude>META-INF/*.SF</exclude>
                                        <exclude>META-INF/*.DSA</exclude>
                                        <exclude>META-INF/*.RSA</exclude>
                                    </excludes>
                                </filter>
                            </filters>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- attach test jar -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.3.0</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>test-jar</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <skipIfEmpty>true</skipIfEmpty>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>1.10</version>
                <executions>
                    <execution>
                        <id>add_sources</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>src/main/java</source>
                            </sources>
                        </configuration>
                    </execution>
                    <execution>
                        <id>add_test_sources</id>
                        <phase>generate-test-sources</phase>
                        <goals>
                            <goal>add-test-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>src/test/java</source>
                            </sources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>3.4.1</version>
                <configuration>
                    <doclint>none</doclint>
                    <source>{{#javaVersion}}{{javaVersion}}{{/javaVersion}}{{^javaVersion}}11{{/javaVersion}}</source>
                </configuration>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.2.1</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>sign-artifacts</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>1.5</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <dependencies>
        {{#swagger1AnnotationLibrary}}
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>${swagger-annotations-version}</version>
        </dependency>
        {{/swagger1AnnotationLibrary}}
        {{#swagger2AnnotationLibrary}}
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>${swagger-annotations-version}</version>
        </dependency>
        {{/swagger2AnnotationLibrary}}

        <!-- @Nullable annotation -->
        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
            <version>3.0.2</version>
        </dependency>


        <!-- HTTP client: jersey-client -->
        <dependency>
            <groupId>com.sun.jersey</groupId>
            <artifactId>jersey-client</artifactId>
            <version>${jersey-version}</version>
        </dependency>
        <dependency>
            <groupId>com.sun.jersey.contribs</groupId>
            <artifactId>jersey-multipart</artifactId>
            <version>${jersey-version}</version>
        </dependency>

        <!-- JSON processing: jackson -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>${jackson-version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>${jackson-version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson-databind-version}</version>
        </dependency>
        {{^useJakartaEe}}
        <dependency>
            <groupId>com.fasterxml.jackson.jaxrs</groupId>
            <artifactId>jackson-jaxrs-json-provider</artifactId>
            <version>${jackson-version}</version>
        </dependency>
        {{/useJakartaEe}}
        {{#useJakartaEe}}
        <dependency>
            <groupId>com.fasterxml.jackson.jakarta.rs</groupId>
            <artifactId>jackson-jakarta-rs-json-provider</artifactId>
            <version>${jackson-version}</version>
        </dependency>
        {{/useJakartaEe}}
        {{#withXml}}

        <!-- XML processing: Jackson -->
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
            <version>${jackson-version}</version>
        </dependency>

        {{/withXml}}
        {{#joda}}
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-joda</artifactId>
                <version>${jackson-version}</version>
            </dependency>
        {{/joda}}
            <dependency>
                <groupId>com.fasterxml.jackson.datatype</groupId>
                <artifactId>jackson-datatype-jsr310</artifactId>
                <version>${jackson-version}</version>
            </dependency>
        {{#useBeanValidation}}
            <!-- Bean Validation API support -->
            <dependency>
                <groupId>jakarta.validation</groupId>
                <artifactId>jakarta.validation-api</artifactId>
                <version>${beanvalidation-version}</version>
                <scope>provided</scope>
            </dependency>
        {{/useBeanValidation}}
        {{#performBeanValidation}}
            <!-- Bean Validation Impl. used to perform BeanValidation -->
            <dependency>
                <groupId>org.hibernate</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>5.4.1.Final</version>
            </dependency>
        {{/performBeanValidation}}
        {{#parcelableModel}}
            <!-- Needed for Parcelable support-->
            <dependency>
                <groupId>com.google.android</groupId>
                <artifactId>android</artifactId>
                <version>4.1.1.4</version>
                <scope>provided</scope>
            </dependency>
        {{/parcelableModel}}
        <dependency>
            <groupId>jakarta.annotation</groupId>
            <artifactId>jakarta.annotation-api</artifactId>
            <version>${jakarta-annotation-version}</version>
            <scope>provided</scope>
        </dependency>
        {{#useReflectionEqualsHashCode}}
        <!-- For equals and hashCode using reflection -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${commons-lang3-version}</version>
        </dependency>
        {{/useReflectionEqualsHashCode}}

        <!-- test dependencies -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-engine</artifactId>
            <version>${junit-version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.platform</groupId>
            <artifactId>junit-platform-runner</artifactId>
            <version>${junit-platform-runner.version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        {{#swagger1AnnotationLibrary}}
        <swagger-annotations-version>1.6.6</swagger-annotations-version>
        {{/swagger1AnnotationLibrary}}
        {{#swagger2AnnotationLibrary}}
        <swagger-annotations-version>2.2.15</swagger-annotations-version>
        {{/swagger2AnnotationLibrary}}
        <jersey-version>1.19.4</jersey-version>
        <jackson-version>2.17.1</jackson-version>
        <jackson-databind-version>2.17.1</jackson-databind-version>
        {{#useJakartaEe}}
        <jakarta-annotation-version>2.1.1</jakarta-annotation-version>
        <beanvalidation-version>3.0.2</beanvalidation-version>
        {{/useJakartaEe}}
        {{^useJakartaEe}}
        <jakarta-annotation-version>1.3.5</jakarta-annotation-version>
        <beanvalidation-version>2.0.2</beanvalidation-version>
        {{/useJakartaEe}}
        {{#useReflectionEqualsHashCode}}
        <commons-lang3-version>3.17.0</commons-lang3-version>
        {{/useReflectionEqualsHashCode}}
        <maven-plugin-version>1.0.0</maven-plugin-version>
        <junit-version>5.10.2</junit-version>
        <junit-platform-runner.version>1.10.0</junit-platform-runner.version>
    </properties>
</project>
