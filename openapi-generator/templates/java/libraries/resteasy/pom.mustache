<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>{{groupId}}</groupId>
    <artifactId>{{artifactId}}</artifactId>
    <packaging>jar</packaging>
    <name>{{artifactId}}</name>
    <version>{{artifactVersion}}</version>
    <scm>
        <connection>{{scmConnection}}</connection>
        <developerConnection>{{scmDeveloperConnection}}</developerConnection>
        <url>{{scmUrl}}</url>
    </scm>
{{#parentOverridden}}
    <parent>
        <groupId>{{{parentGroupId}}}</groupId>
        <artifactId>{{{parentArtifactId}}}</artifactId>
        <version>{{{parentVersion}}}</version>
    </parent>
{{/parentOverridden}}

    <licenses>
        <license>
            <name>{{licenseName}}</name>
            <url>{{licenseUrl}}</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <developers>
        <developer>
            <name>{{developerName}}</name>
            <email>{{developerEmail}}</email>
            <organization>{{developerOrganization}}</organization>
            <organizationUrl>{{developerOrganizationUrl}}</organizationUrl>
        </developer>
    </developers>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>3.0.0-M1</version>
                <executions>
                    <execution>
                        <id>enforce-maven</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireMavenVersion>
                                    <version>2.2.0</version>
                                </requireMavenVersion>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.22.2</version>
                <configuration>
                    <systemPropertyVariables>
                        <property>
                            <name>loggerPath</name>
                            <value>conf/log4j.properties</value>
                        </property>
                    </systemPropertyVariables>
                    <argLine>-Xms512m -Xmx1500m</argLine>
                    <parallel>methods</parallel>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>

            <!-- attach test jar -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>2.6</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>jar</goal>
                            <goal>test-jar</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>1.10</version>
                <executions>
                    <execution>
                        <id>add_sources</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>src/main/java</source>
                            </sources>
                        </configuration>
                    </execution>
                    <execution>
                        <id>add_test_sources</id>
                        <phase>generate-test-sources</phase>
                        <goals>
                            <goal>add-test-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>src/test/java</source>
                            </sources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.5.1</version>
                <configuration>
                   <source>1.8</source>
                   <target>1.8</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>3.3.2</version>
                <configuration>
                    <doclint>none</doclint>
                    <source>1.8</source>
                </configuration>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <dependencies>
        {{#swagger1AnnotationLibrary}}
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>${swagger-annotations-version}</version>
        </dependency>
        {{/swagger1AnnotationLibrary}}
        {{#swagger2AnnotationLibrary}}
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>${swagger-annotations-version}</version>
        </dependency>
        {{/swagger2AnnotationLibrary}}
        <!-- @Nullable annotation -->
        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
            <version>3.0.2</version>
        </dependency>
        <!-- HTTP client: jersey-client -->
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-client</artifactId>
            <version>${resteasy-version}</version>
            <exclusions>
              <exclusion>
                <groupId>org.jboss.resteasy</groupId>
                <artifactId>resteasy-jaxrs-services</artifactId>
              </exclusion>
              <exclusion>
                <groupId>net.jcip</groupId>
                <artifactId>jcip-annotations</artifactId>
                </exclusion>
              </exclusions>
        </dependency>
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-multipart-provider</artifactId>
            <version>${resteasy-version}</version>
            <exclusions>
              <exclusion>
                <groupId>com.sun.xml.bind</groupId>
                <artifactId>jaxb-impl</artifactId>
              </exclusion>
              <exclusion>
                <groupId>com.sun.mail</groupId>
                <artifactId>javax.mail</artifactId>
              </exclusion>
            </exclusions>
        </dependency>
        <!-- JSON processing: jackson -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>${jackson-version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>${jackson-version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson-databind-version}</version>
        </dependency>
        {{#openApiNullable}}
        <dependency>
            <groupId>org.openapitools</groupId>
            <artifactId>jackson-databind-nullable</artifactId>
            <version>${jackson-databind-nullable-version}</version>
        </dependency>
        {{/openApiNullable}}
        {{#withXml}}

        <!-- XML processing: Jackson -->
        <dependency>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
            <version>${jackson-version}</version>
        </dependency>
        <dependency>
            <groupId>io.github.threeten-jaxb</groupId>
            <artifactId>threeten-jaxb-core</artifactId>
            <version>1.2</version>
        </dependency>

        {{/withXml}}
        <dependency>
            <groupId>org.jboss.resteasy</groupId>
            <artifactId>resteasy-jackson2-provider</artifactId>
            <version>${resteasy-version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
            <version>${jackson-version}</version>
        </dependency>
        <dependency>
            <groupId>com.github.joschi.jackson</groupId>
            <artifactId>jackson-datatype-threetenbp</artifactId>
            <version>${threetenbp-version}</version>
        </dependency>
        <dependency>
            <groupId>jakarta.annotation</groupId>
            <artifactId>jakarta.annotation-api</artifactId>
            <version>${jakarta-annotation-version}</version>
            <scope>provided</scope>
        </dependency>
        <!-- test dependencies -->
        <dependency>
            <groupId>org.assertj</groupId>
            <artifactId>assertj-core</artifactId>
            <version>${assertj-version}</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <version>${junit-version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        {{#swagger1AnnotationLibrary}}
        <swagger-annotations-version>1.6.6</swagger-annotations-version>
        {{/swagger1AnnotationLibrary}}
        {{#swagger2AnnotationLibrary}}
        <swagger-annotations-version>2.2.15</swagger-annotations-version>
        {{/swagger2AnnotationLibrary}}
        <resteasy-version>4.7.6.Final</resteasy-version>
        <jackson-version>2.17.1</jackson-version>
        <jackson-databind-version>2.17.1</jackson-databind-version>
        {{#openApiNullable}}
        <jackson-databind-nullable-version>0.2.6</jackson-databind-nullable-version>
        {{/openApiNullable}}
        {{#useJakartaEe}}
        <jakarta-annotation-version>2.1.1</jakarta-annotation-version>
        {{/useJakartaEe}}
        {{^useJakartaEe}}
        <jakarta-annotation-version>1.3.5</jakarta-annotation-version>
        {{/useJakartaEe}}
        <threetenbp-version>2.9.10</threetenbp-version>
        <maven-plugin-version>1.0.0</maven-plugin-version>
        <assertj-version>3.23.1</assertj-version>
        <junit-version>5.10.2</junit-version>
    </properties>
</project>
