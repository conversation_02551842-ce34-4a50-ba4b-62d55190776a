{{>licenseInfo}}

package {{package}};

import {{invokerPackage}}.ApiCallback;
import {{invokerPackage}}.ApiClient;
import {{invokerPackage}}.ApiException;
{{#dynamicOperations}}
import {{invokerPackage}}.ApiOperation;
{{/dynamicOperations}}
import {{invokerPackage}}.ApiResponse;
import {{invokerPackage}}.Configuration;
import {{invokerPackage}}.Pair;
import {{invokerPackage}}.ProgressRequestBody;
import {{invokerPackage}}.ProgressResponseBody;
{{#performBeanValidation}}
import {{invokerPackage}}.BeanValidationException;
{{/performBeanValidation}}

import com.google.gson.reflect.TypeToken;
{{#dynamicOperations}}
import io.swagger.v3.oas.models.Operation;
import io.swagger.v3.oas.models.parameters.Parameter;
{{/dynamicOperations}}

import java.io.IOException;

{{#useBeanValidation}}
import {{javaxPackage}}.validation.constraints.*;
import {{javaxPackage}}.validation.Valid;
{{/useBeanValidation}}
{{#performBeanValidation}}
import {{javaxPackage}}.validation.ConstraintViolation;
import {{javaxPackage}}.validation.Validation;
import {{javaxPackage}}.validation.ValidatorFactory;
import {{javaxPackage}}.validation.executable.ExecutableValidator;
import java.util.Set;
import java.lang.reflect.Method;
import java.lang.reflect.Type;
{{/performBeanValidation}}

{{#imports}}import {{import}};
{{/imports}}

import java.lang.reflect.Type;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
{{#supportStreaming}}
import java.io.InputStream;
{{/supportStreaming}}

{{#operations}}
public class {{classname}} {
    private ApiClient localVarApiClient;
    private int localHostIndex;
    private String localCustomBaseUrl;

    public {{classname}}() {
        this(Configuration.getDefaultApiClient());
    }

    public {{classname}}(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public ApiClient getApiClient() {
        return localVarApiClient;
    }

    public void setApiClient(ApiClient apiClient) {
        this.localVarApiClient = apiClient;
    }

    public int getHostIndex() {
        return localHostIndex;
    }

    public void setHostIndex(int hostIndex) {
        this.localHostIndex = hostIndex;
    }

    public String getCustomBaseUrl() {
        return localCustomBaseUrl;
    }

    public void setCustomBaseUrl(String customBaseUrl) {
        this.localCustomBaseUrl = customBaseUrl;
    }

    {{#operation}}
    {{^vendorExtensions.x-group-parameters}}/**
     * Build call for {{operationId}}{{#allParams}}
     * @param {{paramName}} {{description}}{{#required}} (required){{/required}}{{^required}} (optional{{^isContainer}}{{#defaultValue}}, default to {{.}}{{/defaultValue}}{{/isContainer}}){{/required}}{{/allParams}}
     * @param _callback Callback for upload/download progress
     * @return Call to execute
     * @throws ApiException If fail to serialize the request body object
     {{#responses.0}}
     * @http.response.details
     <table border="1">
       <caption>Response Details</caption>
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        {{#responses}}
        <tr><td> {{code}} </td><td> {{message}} </td><td> {{#headers}} * {{baseName}} - {{description}} <br> {{/headers}}{{^headers.0}} - {{/headers.0}} </td></tr>
        {{/responses}}
     </table>
     {{/responses.0}}
     {{#isDeprecated}}
     * @deprecated
     {{/isDeprecated}}
        {{#externalDocs}}
     * {{description}}
     * @see <a href="{{url}}">{{summary}} Documentation</a>
        {{/externalDocs}}
     */
    {{#isDeprecated}}
    @Deprecated
    {{/isDeprecated}}
    public{{/vendorExtensions.x-group-parameters}}{{#vendorExtensions.x-group-parameters}}private{{/vendorExtensions.x-group-parameters}} okhttp3.Call {{operationId}}Call({{#allParams}}{{>nullable_var_annotations}} {{{dataType}}} {{paramName}}, {{/allParams}}final ApiCallback _callback) throws ApiException {
        String basePath = null;
        // Operation Servers
        String[] localBasePaths = new String[] { {{#servers}}"{{{url}}}"{{^-last}}, {{/-last}}{{/servers}} };

        // Determine Base Path to Use
        if (localCustomBaseUrl != null){
            basePath = localCustomBaseUrl;
        } else if ( localBasePaths.length > 0 ) {
            basePath = localBasePaths[localHostIndex];
        } else {
            basePath = null;
        }

        Object localVarPostBody = {{#bodyParam}}{{paramName}}{{/bodyParam}}{{^bodyParam}}null{{/bodyParam}};

        // create path and map variables
        {{^dynamicOperations}}
        String localVarPath = "{{{path}}}"{{#pathParams}}
            .replace("{" + "{{baseName}}" + "}", localVarApiClient.escapeString({{#collectionFormat}}localVarApiClient.collectionPathParameterToString("{{{collectionFormat}}}", {{{paramName}}}){{/collectionFormat}}{{^collectionFormat}}{{{paramName}}}.toString(){{/collectionFormat}})){{/pathParams}};
        {{/dynamicOperations}}
        {{#dynamicOperations}}
        ApiOperation apiOperation = localVarApiClient.getOperationLookupMap().get("{{{operationId}}}");
        if (apiOperation == null) {
            throw new ApiException("Operation not found in OAS");
        }
        Operation operation = apiOperation.getOperation();
        String localVarPath = apiOperation.getPath();
        Map<String, Object> paramMap = new HashMap<>();
        {{#allParams}}
            {{^isFormParam}}
            {{^isBodyParam}}
        paramMap.put("{{baseName}}", {{paramName}});
            {{/isBodyParam}}
            {{/isFormParam}}
        {{/allParams}}
        {{/dynamicOperations}}

        List<Pair> localVarQueryParams = new ArrayList<Pair>();
        List<Pair> localVarCollectionQueryParams = new ArrayList<Pair>();
        Map<String, String> localVarHeaderParams = new HashMap<String, String>();
        Map<String, String> localVarCookieParams = new HashMap<String, String>();
        Map<String, Object> localVarFormParams = new HashMap<String, Object>();

        {{#formParams}}
        if ({{paramName}} != null) {
            localVarFormParams.put("{{baseName}}", {{paramName}});
        }

        {{/formParams}}
        {{^dynamicOperations}}
        {{#queryParams}}
        if ({{paramName}} != null) {
            {{#isFreeFormObject}}localVarQueryParams.addAll(localVarApiClient.freeFormParameterToPairs({{paramName}}));{{/isFreeFormObject}}{{^isFreeFormObject}}{{#collectionFormat}}localVarCollectionQueryParams.addAll(localVarApiClient.parameterToPairs("{{{.}}}", {{/collectionFormat}}{{^collectionFormat}}localVarQueryParams.addAll(localVarApiClient.parameterToPair({{/collectionFormat}}"{{baseName}}", {{paramName}}));{{/isFreeFormObject}}
        }

        {{/queryParams}}
        {{#constantParams}}
        {{#isQueryParam}}
        // Set client side default value of Query Param "{{baseName}}".
        localVarCollectionQueryParams.add(new Pair("{{baseName}}", {{#_enum}}"{{{.}}}"{{/_enum}}));

        {{/isQueryParam}}
        {{/constantParams}}
        {{#constantParams}}
        {{#isHeaderParam}}
        // Set client side default value of Header Param "{{baseName}}".
        localVarHeaderParams.put("{{baseName}}", {{#_enum}}"{{{.}}}"{{/_enum}});

        {{/isHeaderParam}}
        {{/constantParams}}
        {{#cookieParams}}
        if ({{paramName}} != null) {
            localVarCookieParams.put("{{baseName}}", localVarApiClient.parameterToString({{paramName}}));
        }

        {{/cookieParams}}
        {{#constantParams}}
        {{#isCookieParam}}
        // Set client side default value of Cookie Param "{{baseName}}".
        localVarCookieParams.put("{{baseName}}", {{#_enum}}"{{{.}}}"{{/_enum}});

        {{/isCookieParam}}
        {{/constantParams}}
        {{/dynamicOperations}}
        {{#dynamicOperations}}
        localVarPath = localVarApiClient.fillParametersFromOperation(operation, paramMap, localVarPath, localVarQueryParams, localVarCollectionQueryParams, localVarHeaderParams, localVarCookieParams);

        {{/dynamicOperations}}
        final String[] localVarAccepts = {
            {{#produces}}
            "{{{mediaType}}}"{{^-last}},{{/-last}}
            {{/produces}}
        };
        final String localVarAccept = localVarApiClient.selectHeaderAccept(localVarAccepts);
        if (localVarAccept != null) {
            localVarHeaderParams.put("Accept", localVarAccept);
        }

        final String[] localVarContentTypes = {
            {{#consumes}}
            "{{{mediaType}}}"{{^-last}},{{/-last}}
            {{/consumes}}
        };
        final String localVarContentType = localVarApiClient.selectHeaderContentType(localVarContentTypes);
        if (localVarContentType != null) {
            localVarHeaderParams.put("Content-Type", localVarContentType);
        }
        {{^dynamicOperations}}
        {{#headerParams}}

        if ({{paramName}} != null) {
            localVarHeaderParams.put("{{baseName}}", localVarApiClient.parameterToString({{paramName}}));
        }

        {{/headerParams}}
        {{/dynamicOperations}}

        String[] localVarAuthNames = new String[] { {{#withAWSV4Signature}}"AWS4Auth"{{/withAWSV4Signature}}{{#authMethods}}{{#-first}}{{#withAWSV4Signature}}, {{/withAWSV4Signature}}{{/-first}}"{{name}}"{{^-last}}, {{/-last}}{{/authMethods}} };
        return localVarApiClient.buildCall(basePath, localVarPath, {{^dynamicOperations}}"{{httpMethod}}"{{/dynamicOperations}}{{#dynamicOperations}}apiOperation.getMethod(){{/dynamicOperations}}, localVarQueryParams, localVarCollectionQueryParams, localVarPostBody, localVarHeaderParams, localVarCookieParams, localVarFormParams, localVarAuthNames, _callback);
    }

    {{#isDeprecated}}
    @Deprecated
    {{/isDeprecated}}
    @SuppressWarnings("rawtypes")
    private okhttp3.Call {{operationId}}ValidateBeforeCall({{#allParams}}{{>nullable_var_annotations}} {{{dataType}}} {{paramName}}, {{/allParams}}final ApiCallback _callback) throws ApiException {
        {{^performBeanValidation}}
        {{#allParams}}
        {{#required}}
        // verify the required parameter '{{paramName}}' is set
        if ({{paramName}} == null) {
            throw new ApiException("Missing the required parameter '{{paramName}}' when calling {{operationId}}(Async)");
        }

        {{/required}}
        {{/allParams}}
        return {{operationId}}Call({{#allParams}}{{paramName}}, {{/allParams}}_callback);

        {{/performBeanValidation}}
        {{#performBeanValidation}}
        try {
            ValidatorFactory factory = Validation.buildDefaultValidatorFactory();
            ExecutableValidator executableValidator = factory.getValidator().forExecutables();

            Object[] parameterValues = { {{#allParams}}{{paramName}}{{^-last}}, {{/-last}}{{/allParams}} };
            Method method = this.getClass().getMethod("{{operationId}}WithHttpInfo"{{#allParams}}, {{#isArray}}java.util.List{{/isArray}}{{#isMap}}java.util.Map{{/isMap}}{{^isArray}}{{^isMap}}{{>nullable_var_annotations}} {{{dataType}}}{{/isMap}}{{/isArray}}.class{{/allParams}});
            Set<ConstraintViolation<{{classname}}>> violations = executableValidator.validateParameters(this, method,
                    parameterValues);

            if (violations.size() == 0) {
                return {{operationId}}Call({{#allParams}}{{paramName}}, {{/allParams}}_callback);
            } else {
                throw new BeanValidationException((Set) violations);
            }
        } catch (NoSuchMethodException e) {
            e.printStackTrace();
            throw new ApiException(e.getMessage());
        } catch (SecurityException e) {
            e.printStackTrace();
            throw new ApiException(e.getMessage());
        }
        {{/performBeanValidation}}
    }

    {{^vendorExtensions.x-group-parameters}}
    /**
     * {{summary}}
     * {{notes}}{{#allParams}}
     * @param {{paramName}} {{description}}{{#required}} (required){{/required}}{{^required}} (optional{{^isContainer}}{{#defaultValue}}, default to {{.}}{{/defaultValue}}{{/isContainer}}){{/required}}{{/allParams}}{{#returnType}}
     * @return {{.}}{{/returnType}}
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     {{#responses.0}}
     * @http.response.details
     <table border="1">
       <caption>Response Details</caption>
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        {{#responses}}
        <tr><td> {{code}} </td><td> {{message}} </td><td> {{#headers}} * {{baseName}} - {{description}} <br> {{/headers}}{{^headers.0}} - {{/headers.0}} </td></tr>
        {{/responses}}
     </table>
     {{/responses.0}}
     {{#isDeprecated}}
     * @deprecated
     {{/isDeprecated}}
        {{#externalDocs}}
     * {{description}}
     * @see <a href="{{url}}">{{summary}} Documentation</a>
        {{/externalDocs}}
     */
    {{#isDeprecated}}
    @Deprecated
    {{/isDeprecated}}
    {{#vendorExtensions.x-streaming}}
    public {{#returnType}}InputStream {{/returnType}}{{^returnType}}void {{/returnType}}{{operationId}}({{#allParams}}{{>nullable_var_annotations}} {{{dataType}}} {{paramName}}{{^-last}}, {{/-last}}{{/allParams}}) throws ApiException {
        {{#returnType}}InputStream localVarResp = {{/returnType}}{{operationId}}WithHttpInfo({{#allParams}}{{paramName}}{{^-last}}, {{/-last}}{{/allParams}});{{#returnType}}
        return localVarResp;{{/returnType}}
    }
    {{/vendorExtensions.x-streaming}}
    {{^vendorExtensions.x-streaming}}
    public {{#returnType}}{{{.}}} {{/returnType}}{{^returnType}}void {{/returnType}}{{operationId}}({{#allParams}}{{>nullable_var_annotations}} {{{dataType}}} {{paramName}}{{^-last}}, {{/-last}}{{/allParams}}) throws ApiException {
        {{#returnType}}ApiResponse<{{{.}}}> localVarResp = {{/returnType}}{{operationId}}WithHttpInfo({{#allParams}}{{paramName}}{{^-last}}, {{/-last}}{{/allParams}});{{#returnType}}
        return localVarResp.getData();{{/returnType}}
    }
    {{/vendorExtensions.x-streaming}}
    {{/vendorExtensions.x-group-parameters}}

    {{^vendorExtensions.x-group-parameters}}/**
     * {{summary}}
     * {{notes}}{{#allParams}}
     * @param {{paramName}} {{description}}{{#required}} (required){{/required}}{{^required}} (optional{{^isContainer}}{{#defaultValue}}, default to {{.}}{{/defaultValue}}{{/isContainer}}){{/required}}{{/allParams}}
     * @return ApiResponse&lt;{{returnType}}{{^returnType}}Void{{/returnType}}&gt;
     * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
     {{#responses.0}}
     * @http.response.details
     <table border="1">
       <caption>Response Details</caption>
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        {{#responses}}
        <tr><td> {{code}} </td><td> {{message}} </td><td> {{#headers}} * {{baseName}} - {{description}} <br> {{/headers}}{{^headers.0}} - {{/headers.0}} </td></tr>
        {{/responses}}
     </table>
     {{/responses.0}}
     {{#isDeprecated}}
     * @deprecated
     {{/isDeprecated}}
        {{#externalDocs}}
     * {{description}}
     * @see <a href="{{url}}">{{summary}} Documentation</a>
        {{/externalDocs}}
     */
    {{#isDeprecated}}
    @Deprecated
    {{/isDeprecated}}
    public{{/vendorExtensions.x-group-parameters}}{{#vendorExtensions.x-group-parameters}}private{{/vendorExtensions.x-group-parameters}}{{#vendorExtensions.x-streaming}} InputStream {{operationId}}WithHttpInfo({{#allParams}}{{#useBeanValidation}}{{>beanValidationQueryParams}}{{/useBeanValidation}}{{>nullable_var_annotations}} {{{dataType}}} {{paramName}}{{^-last}}, {{/-last}}{{/allParams}}) throws ApiException {
        okhttp3.Call localVarCall = {{operationId}}ValidateBeforeCall({{#allParams}}{{paramName}}, {{/allParams}}null);
        {{#returnType}}
        {{#errorObjectType}}
        try {
            Type localVarReturnType = new TypeToken<{{{returnType}}}>(){}.getType();
            return localVarApiClient.executeStream(localVarCall, localVarReturnType);
        } catch (ApiException e) {
            e.setErrorObject(localVarApiClient.getJSON().getGson().fromJson(e.getResponseBody(), new TypeToken<{{{errorObjectType}}}>(){}.getType()));
            throw e;
        }
        {{/errorObjectType}}
        {{^errorObjectType}}
        Type localVarReturnType = new TypeToken<{{{returnType}}}>(){}.getType();
        return localVarApiClient.executeStream(localVarCall, localVarReturnType);
        {{/errorObjectType}}
        {{/returnType}}
    }
    {{/vendorExtensions.x-streaming}}{{^vendorExtensions.x-streaming}} ApiResponse<{{#returnType}}{{{returnType}}}{{/returnType}}{{^returnType}}Void{{/returnType}}> {{operationId}}WithHttpInfo({{#allParams}}{{#useBeanValidation}}{{>beanValidationQueryParams}}{{/useBeanValidation}}{{>nullable_var_annotations}} {{{dataType}}} {{paramName}}{{^-last}}, {{/-last}}{{/allParams}}) throws ApiException {
        okhttp3.Call localVarCall = {{operationId}}ValidateBeforeCall({{#allParams}}{{paramName}}, {{/allParams}}null);
        {{^returnType}}
        return localVarApiClient.execute(localVarCall);
        {{/returnType}}
        {{#returnType}}
        {{#errorObjectType}}
        try {
            Type localVarReturnType = new TypeToken<{{{returnType}}}>(){}.getType();
            return localVarApiClient.execute(localVarCall, localVarReturnType);
        } catch (ApiException e) {
            e.setErrorObject(localVarApiClient.getJSON().getGson().fromJson(e.getResponseBody(), new TypeToken<{{{errorObjectType}}}>(){}.getType()));
            throw e;
        }
        {{/errorObjectType}}
        {{^errorObjectType}}
        Type localVarReturnType = new TypeToken<{{{returnType}}}>(){}.getType();
        return localVarApiClient.execute(localVarCall, localVarReturnType);
        {{/errorObjectType}}
        {{/returnType}}
    }
    {{/vendorExtensions.x-streaming}}

    {{^vendorExtensions.x-group-parameters}}/**
     * {{summary}} (asynchronously)
     * {{notes}}{{#allParams}}
     * @param {{paramName}} {{description}}{{#required}} (required){{/required}}{{^required}} (optional{{^isContainer}}{{#defaultValue}}, default to {{.}}{{/defaultValue}}{{/isContainer}}){{/required}}{{/allParams}}
     * @param _callback The callback to be executed when the API call finishes
     * @return The request call
     * @throws ApiException If fail to process the API call, e.g. serializing the request body object
     {{#responses.0}}
     * @http.response.details
     <table border="1">
       <caption>Response Details</caption>
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        {{#responses}}
        <tr><td> {{code}} </td><td> {{message}} </td><td> {{#headers}} * {{baseName}} - {{description}} <br> {{/headers}}{{^headers.0}} - {{/headers.0}} </td></tr>
        {{/responses}}
     </table>
     {{/responses.0}}
     {{#isDeprecated}}
     * @deprecated
     {{/isDeprecated}}
        {{#externalDocs}}
     * {{description}}
     * @see <a href="{{url}}">{{summary}} Documentation</a>
        {{/externalDocs}}
     */
    {{#isDeprecated}}
    @Deprecated
    {{/isDeprecated}}
    public{{/vendorExtensions.x-group-parameters}}{{#vendorExtensions.x-group-parameters}}private{{/vendorExtensions.x-group-parameters}} okhttp3.Call {{operationId}}Async({{#allParams}}{{>nullable_var_annotations}} {{{dataType}}} {{paramName}}, {{/allParams}}final ApiCallback<{{{returnType}}}{{^returnType}}Void{{/returnType}}> _callback) throws ApiException {

        okhttp3.Call localVarCall = {{operationId}}ValidateBeforeCall({{#allParams}}{{paramName}}, {{/allParams}}_callback);
        {{#returnType}}Type localVarReturnType = new TypeToken<{{{returnType}}}>(){}.getType();
        localVarApiClient.executeAsync(localVarCall, localVarReturnType, _callback);{{/returnType}}{{^returnType}}localVarApiClient.executeAsync(localVarCall, _callback);{{/returnType}}
        return localVarCall;
    }
    {{#vendorExtensions.x-group-parameters}}

    public class API{{operationId}}Request {
        {{#requiredParams}}
        {{>nullable_var_annotations}}
        private final {{{dataType}}} {{paramName}};
        {{/requiredParams}}
        {{#optionalParams}}
        {{>nullable_var_annotations}}
        private {{{dataType}}} {{paramName}};
        {{/optionalParams}}

        private API{{operationId}}Request({{#requiredParams}}{{>nullable_var_annotations}} {{{dataType}}} {{paramName}}{{^-last}}, {{/-last}}{{/requiredParams}}) {
            {{#requiredParams}}
            this.{{paramName}} = {{paramName}};
            {{/requiredParams}}
        }

        {{#optionalParams}}
        /**
         * Set {{paramName}}
         * @param {{paramName}} {{description}} (optional{{^isContainer}}{{#defaultValue}}, default to {{.}}{{/defaultValue}}{{/isContainer}})
         * @return API{{operationId}}Request
         */
        public API{{operationId}}Request {{paramName}}({{>nullable_var_annotations}} {{{dataType}}} {{paramName}}) {
            this.{{paramName}} = {{paramName}};
            return this;
        }

        {{/optionalParams}}
        /**
         * Build call for {{operationId}}
         * @param _callback ApiCallback API callback
         * @return Call to execute
         * @throws ApiException If fail to serialize the request body object
         {{#responses.0}}
         * @http.response.details
         <table border="1">
       <caption>Response Details</caption>
            <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
            {{#responses}}
            <tr><td> {{code}} </td><td> {{message}} </td><td> {{#headers}} * {{baseName}} - {{description}} <br> {{/headers}}{{^headers.0}} - {{/headers.0}} </td></tr>
            {{/responses}}
         </table>
         {{/responses.0}}
         {{#isDeprecated}}
         * @deprecated
         {{/isDeprecated}}
         */
        {{#isDeprecated}}
        @Deprecated
        {{/isDeprecated}}
        public okhttp3.Call buildCall(final ApiCallback _callback) throws ApiException {
            return {{operationId}}Call({{#allParams}}{{paramName}}, {{/allParams}}_callback);
        }

        /**
         * Execute {{operationId}} request{{#returnType}}
         * @return {{.}}{{/returnType}}
         * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
         {{#responses.0}}
         * @http.response.details
         <table border="1">
       <caption>Response Details</caption>
            <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
            {{#responses}}
            <tr><td> {{code}} </td><td> {{message}} </td><td> {{#headers}} * {{baseName}} - {{description}} <br> {{/headers}}{{^headers.0}} - {{/headers.0}} </td></tr>
            {{/responses}}
         </table>
         {{/responses.0}}
         {{#isDeprecated}}
         * @deprecated
         {{/isDeprecated}}
         */
        {{#isDeprecated}}
        @Deprecated
        {{/isDeprecated}}
        {{^vendorExtensions.x-streaming}}
        public {{{returnType}}}{{^returnType}}void{{/returnType}} execute() throws ApiException {
            {{#returnType}}ApiResponse<{{{.}}}> localVarResp = {{/returnType}}{{operationId}}WithHttpInfo({{#allParams}}{{paramName}}{{^-last}}, {{/-last}}{{/allParams}});{{#returnType}}
            return localVarResp.getData();{{/returnType}}
        }
        {{/vendorExtensions.x-streaming}}
        {{#vendorExtensions.x-streaming}}
        public InputStream execute() throws ApiException {
            return {{operationId}}WithHttpInfo({{#allParams}}{{paramName}}{{^-last}}, {{/-last}}{{/allParams}});
        }
        {{/vendorExtensions.x-streaming}}

        /**
         * Execute {{operationId}} request with HTTP info returned
         * @return ApiResponse&lt;{{returnType}}{{^returnType}}Void{{/returnType}}&gt;
         * @throws ApiException If fail to call the API, e.g. server error or cannot deserialize the response body
         {{#responses.0}}
         * @http.response.details
         <table border="1">
       <caption>Response Details</caption>
            <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
            {{#responses}}
            <tr><td> {{code}} </td><td> {{message}} </td><td> {{#headers}} * {{baseName}} - {{description}} <br> {{/headers}}{{^headers.0}} - {{/headers.0}} </td></tr>
            {{/responses}}
         </table>
         {{/responses.0}}
         {{#isDeprecated}}
         * @deprecated
         {{/isDeprecated}}
         */
        {{#isDeprecated}}
        @Deprecated
        {{/isDeprecated}}
        {{^vendorExtensions.x-streaming}}
        public ApiResponse<{{{returnType}}}{{^returnType}}Void{{/returnType}}> executeWithHttpInfo() throws ApiException {
            return {{operationId}}WithHttpInfo({{#allParams}}{{paramName}}{{^-last}}, {{/-last}}{{/allParams}});
        }
        {{/vendorExtensions.x-streaming}}
        {{#vendorExtensions.x-streaming}}
        public InputStream executeWithHttpInfo() throws ApiException {
            return {{operationId}}WithHttpInfo({{#allParams}}{{paramName}}{{^-last}}, {{/-last}}{{/allParams}});
        }
        {{/vendorExtensions.x-streaming}}

        /**
         * Execute {{operationId}} request (asynchronously)
         * @param _callback The callback to be executed when the API call finishes
         * @return The request call
         * @throws ApiException If fail to process the API call, e.g. serializing the request body object
         {{#responses.0}}
         * @http.response.details
         <table border="1">
       <caption>Response Details</caption>
            <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
            {{#responses}}
            <tr><td> {{code}} </td><td> {{message}} </td><td> {{#headers}} * {{baseName}} - {{description}} <br> {{/headers}}{{^headers.0}} - {{/headers.0}} </td></tr>
            {{/responses}}
         </table>
         {{/responses.0}}
         {{#isDeprecated}}
         * @deprecated
         {{/isDeprecated}}
         */
        {{#isDeprecated}}
        @Deprecated
        {{/isDeprecated}}
        public okhttp3.Call executeAsync(final ApiCallback<{{{returnType}}}{{^returnType}}Void{{/returnType}}> _callback) throws ApiException {
            return {{operationId}}Async({{#allParams}}{{paramName}}, {{/allParams}}_callback);
        }
    }

    /**
     * {{summary}}
     * {{notes}}{{#requiredParams}}
     * @param {{paramName}} {{description}} (required){{/requiredParams}}
     * @return API{{operationId}}Request
     {{#responses.0}}
     * @http.response.details
     <table border="1">
       <caption>Response Details</caption>
        <tr><td> Status Code </td><td> Description </td><td> Response Headers </td></tr>
        {{#responses}}
        <tr><td> {{code}} </td><td> {{message}} </td><td> {{#headers}} * {{baseName}} - {{description}} <br> {{/headers}}{{^headers.0}} - {{/headers.0}} </td></tr>
        {{/responses}}
     </table>
     {{/responses.0}}
     {{#isDeprecated}}
     * @deprecated
     {{/isDeprecated}}
        {{#externalDocs}}
     * {{description}}
     * @see <a href="{{url}}">{{summary}} Documentation</a>
        {{/externalDocs}}
     */
    {{#isDeprecated}}
    @Deprecated
    {{/isDeprecated}}
    public API{{operationId}}Request {{operationId}}({{#requiredParams}}{{>nullable_var_annotations}} {{{dataType}}} {{paramName}}{{^-last}}, {{/-last}}{{/requiredParams}}) {
        return new API{{operationId}}Request({{#requiredParams}}{{paramName}}{{^-last}}, {{/-last}}{{/requiredParams}});
    }
    {{/vendorExtensions.x-group-parameters}}
    {{/operation}}
}
{{/operations}}
