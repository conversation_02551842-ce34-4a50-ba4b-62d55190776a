# {{artifactId}}

{{appName}}
- API version: {{appVersion}}
{{^hideGenerationTimestamp}}
  - Build date: {{generatedDate}}
{{/hideGenerationTimestamp}}
  - Generator version: {{generatorVersion}}

{{{appDescriptionWithNewLines}}}

{{#infoUrl}}
  For more information, please visit [{{{infoUrl}}}]({{{infoUrl}}})
{{/infoUrl}}

*Automatically generated by the [OpenAPI Generator](https://openapi-generator.tech)*


## Requirements

Building the API client library requires:
1. Java 1.8+
2. Maven (3.8.3+)/Gradle (7.2+)

## Installation

To install the API client library to your local Maven repository, simply execute:

```shell
mvn clean install
```

To deploy it to a remote Maven repository instead, configure the settings of the repository and execute:

```shell
mvn clean deploy
```

Refer to the [OSSRH Guide](http://central.sonatype.org/pages/ossrh-guide.html) for more information.

### Maven users

Add this dependency to your project's POM:

```xml
<dependency>
  <groupId>{{{groupId}}}</groupId>
  <artifactId>{{{artifactId}}}</artifactId>
  <version>{{{artifactVersion}}}</version>
  <scope>compile</scope>
</dependency>
```

### Gradle users

Add this dependency to your project's build file:

```groovy
  repositories {
    mavenCentral()     // Needed if the '{{{artifactId}}}' jar has been published to maven central.
    mavenLocal()       // Needed if the '{{{artifactId}}}' jar has been published to the local maven repo.
  }

  dependencies {
     implementation "{{{groupId}}}:{{{artifactId}}}:{{{artifactVersion}}}"
  }
```

### Others

At first generate the JAR by executing:

```shell
mvn clean package
```

Then manually install the following JARs:

* `target/{{{artifactId}}}-{{{artifactVersion}}}.jar`
* `target/lib/*.jar`

## Getting Started

Please follow the [installation](#installation) instruction and execute the following Java code:

```java
{{#apiInfo}}{{#apis}}{{#-first}}{{#operations}}{{#operation}}{{#-first}}
// Import classes:
import {{{invokerPackage}}}.ApiClient;
import {{{invokerPackage}}}.ApiException;
import {{{invokerPackage}}}.Configuration;{{#hasAuthMethods}}
import {{{invokerPackage}}}.auth.*;{{/hasAuthMethods}}
import {{{modelPackage}}}.*;
import {{{package}}}.{{{classname}}};

public class Example {
  public static void main(String[] args) {
    ApiClient defaultClient = Configuration.getDefaultApiClient();
    defaultClient.setBasePath("{{{basePath}}}");
    {{#withAWSV4Signature}}
    // Configure AWS Signature V4 authorization
    defaultClient.setAWS4Configuration("YOUR_ACCESS_KEY", "YOUR_SECRET_KEY", "REGION", "SERVICE")
    {{/withAWSV4Signature}}
    {{#hasAuthMethods}}
    {{#authMethods}}{{#isBasic}}{{#isBasicBasic}}
    // Configure HTTP basic authorization: {{{name}}}
    HttpBasicAuth {{{name}}} = (HttpBasicAuth) defaultClient.getAuthentication("{{{name}}}");
    {{{name}}}.setUsername("YOUR USERNAME");
    {{{name}}}.setPassword("YOUR PASSWORD");{{/isBasicBasic}}{{#isBasicBearer}}
    // Configure HTTP bearer authorization: {{{name}}}
    HttpBearerAuth {{{name}}} = (HttpBearerAuth) defaultClient.getAuthentication("{{{name}}}");
    {{{name}}}.setBearerToken("BEARER TOKEN");{{/isBasicBearer}}{{/isBasic}}{{#isApiKey}}
    // Configure API key authorization: {{{name}}}
    ApiKeyAuth {{{name}}} = (ApiKeyAuth) defaultClient.getAuthentication("{{{name}}}");
    {{{name}}}.setApiKey("YOUR API KEY");
    // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
    //{{{name}}}.setApiKeyPrefix("Token");{{/isApiKey}}{{#isOAuth}}
    // Configure OAuth2 access token for authorization: {{{name}}}
    OAuth {{{name}}} = (OAuth) defaultClient.getAuthentication("{{{name}}}");
    {{{name}}}.setAccessToken("YOUR ACCESS TOKEN");{{/isOAuth}}
    {{/authMethods}}
    {{/hasAuthMethods}}

    {{{classname}}} apiInstance = new {{{classname}}}(defaultClient);
    {{#allParams}}
    {{{dataType}}} {{{paramName}}} = {{{example}}}; // {{{dataType}}} | {{{description}}}
    {{/allParams}}
    try {
      {{#returnType}}{{{.}}} result = {{/returnType}}apiInstance.{{{operationId}}}{{^vendorExtensions.x-group-parameters}}({{#allParams}}{{{paramName}}}{{^-last}}, {{/-last}}{{/allParams}});{{/vendorExtensions.x-group-parameters}}{{#vendorExtensions.x-group-parameters}}({{#requiredParams}}{{{paramName}}}{{^-last}}, {{/-last}}{{/requiredParams}}){{#optionalParams}}
            .{{{paramName}}}({{{paramName}}}){{/optionalParams}}
            .execute();{{/vendorExtensions.x-group-parameters}}{{#returnType}}
      System.out.println(result);{{/returnType}}
    } catch (ApiException e) {
      System.err.println("Exception when calling {{{classname}}}#{{{operationId}}}");
      System.err.println("Status code: " + e.getCode());
      System.err.println("Reason: " + e.getResponseBody());
      System.err.println("Response headers: " + e.getResponseHeaders());
      e.printStackTrace();
    }
  }
}
{{/-first}}{{/operation}}{{/operations}}{{/-first}}{{/apis}}{{/apiInfo}}
```

## Documentation for API Endpoints

All URIs are relative to *{{basePath}}*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------
{{#apiInfo}}{{#apis}}{{#operations}}{{#operation}}*{{classname}}* | [**{{operationId}}**]({{apiDocPath}}{{classname}}.md#{{operationId}}) | **{{httpMethod}}** {{path}} | {{summary}}
{{/operation}}{{/operations}}{{/apis}}{{/apiInfo}}

## Documentation for Models

{{#models}}{{#model}} - [{{classname}}]({{modelDocPath}}{{classname}}.md)
{{/model}}{{/models}}

<a id="documentation-for-authorization"></a>
## Documentation for Authorization

{{^authMethods}}Endpoints do not require authorization.{{/authMethods}}
{{#hasAuthMethods}}Authentication schemes defined for the API:{{/hasAuthMethods}}
{{#authMethods}}
<a id="{{name}}"></a>
### {{name}}

{{#isApiKey}}- **Type**: API key
- **API key parameter name**: {{keyParamName}}
- **Location**: {{#isKeyInQuery}}URL query string{{/isKeyInQuery}}{{#isKeyInHeader}}HTTP header{{/isKeyInHeader}}
{{/isApiKey}}
{{#isBasicBasic}}- **Type**: HTTP basic authentication
{{/isBasicBasic}}
{{#isBasicBearer}}- **Type**: HTTP Bearer Token authentication{{#bearerFormat}} ({{{.}}}){{/bearerFormat}}
{{/isBasicBearer}}
{{#isHttpSignature}}- **Type**: HTTP signature authentication
{{/isHttpSignature}}
{{#isOAuth}}- **Type**: OAuth
- **Flow**: {{flow}}
- **Authorization URL**: {{authorizationUrl}}
- **Scopes**: {{^scopes}}N/A{{/scopes}}
{{#scopes}}  - {{scope}}: {{description}}
{{/scopes}}
{{/isOAuth}}

{{/authMethods}}

## Recommendation

It's recommended to create an instance of `ApiClient` per thread in a multithreaded environment to avoid any potential issues.

## Author

{{#apiInfo}}{{#apis}}{{#-last}}{{infoEmail}}
{{/-last}}{{/apis}}{{/apiInfo}}
