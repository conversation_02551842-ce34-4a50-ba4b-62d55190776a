apply plugin: 'idea'
apply plugin: 'eclipse'

group = '{{groupId}}'
version = '{{artifactVersion}}'

buildscript {
    repositories {
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:1.5.+'
        classpath 'com.github.dcendents:android-maven-gradle-plugin:1.3'
    }
}

repositories {
    mavenCentral()
}


if(hasProperty('target') && target == 'android') {

    apply plugin: 'com.android.library'
    apply plugin: 'com.github.dcendents.android-maven'

    android {
        compileSdkVersion 23
        buildToolsVersion '23.0.2'
        defaultConfig {
            minSdkVersion 14
            targetSdkVersion 22
        }
        compileOptions {
            sourceCompatibility JavaVersion.VERSION_1_8
            targetCompatibility JavaVersion.VERSION_1_8
        }

        // Rename the aar correctly
        libraryVariants.all { variant ->
            variant.outputs.each { output ->
                def outputFile = output.outputFile
                if (outputFile != null && outputFile.name.endsWith('.aar')) {
                    def fileName = "${project.name}-${variant.baseName}-${version}.aar"
                    output.outputFile = new File(outputFile.parent, fileName)
                }
            }
        }

        dependencies {
            provided "jakarta.annotation:jakarta.annotation-api:$jakarta_annotation_version"
        }
    }

    afterEvaluate {
        android.libraryVariants.all { variant ->
            def task = project.tasks.create "jar${variant.name.capitalize()}", Jar
            task.description = "Create jar artifact for ${variant.name}"
            task.dependsOn variant.javaCompile
            task.from variant.javaCompile.destinationDirectory
            task.destinationDirectory = project.file("${project.buildDir}/outputs/jar")
            task.archiveFileName = "${project.name}-${variant.baseName}-${version}.jar"
            artifacts.add('archives', task);
        }
    }

    task sourcesJar(type: Jar) {
        from android.sourceSets.main.java.srcDirs
        classifier = 'sources'
    }

    artifacts {
        archives sourcesJar
    }

} else {

    apply plugin: 'java'
    apply plugin: 'maven-publish'

    sourceCompatibility = JavaVersion.VERSION_1_8
    targetCompatibility = JavaVersion.VERSION_1_8

    publishing {
        publications {
            maven(MavenPublication) {
               artifactId = '{{artifactId}}'
               from components.java
            }
        }
    }

    task execute(type:JavaExec) {
       main = System.getProperty('mainClass')
       classpath = sourceSets.main.runtimeClasspath
    }
}

ext {
    {{#swagger1AnnotationLibrary}}
    swagger_annotations_version = "1.6.6"
    {{/swagger1AnnotationLibrary}}
    {{#swagger2AnnotationLibrary}}
    swagger_annotations_version = "2.2.15"
    {{/swagger2AnnotationLibrary}}
    rest_assured_version = "5.3.2"
    junit_version = "5.10.3"
{{#jackson}}
    jackson_version = "2.17.1"
    jackson_databind_version = "2.17.1"
    {{#openApiNullable}}
    jackson_databind_nullable_version = "0.2.6"
    {{/openApiNullable}}
{{/jackson}}
{{#gson}}
    gson_version = "2.10.1"
    gson_fire_version = "1.9.0"
{{/gson}}
{{#joda}}
    jodatime_version = "2.10.5"
{{/joda}}
    okio_version = "3.6.0"
    jakarta_annotation_version = "1.3.5"
}

dependencies {
    {{#swagger1AnnotationLibrary}}
    implementation "io.swagger:swagger-annotations:$swagger_annotations_version"
    {{/swagger1AnnotationLibrary}}
    {{#swagger2AnnotationLibrary}}
    implementation "io.swagger.core.v3:swagger-annotations:$swagger_annotations_version"
    {{/swagger2AnnotationLibrary}}
    implementation "com.google.code.findbugs:jsr305:3.0.2"
    implementation "io.rest-assured:rest-assured:$rest_assured_version"
{{#jackson}}
    implementation "com.fasterxml.jackson.core:jackson-core:$jackson_version"
    implementation "com.fasterxml.jackson.core:jackson-annotations:$jackson_version"
    implementation "com.fasterxml.jackson.core:jackson-databind:$jackson_databind_version"
    implementation "com.fasterxml.jackson.jaxrs:jackson-jaxrs-json-provider:$jackson_version"
    {{#openApiNullable}}
    implementation "org.openapitools:jackson-databind-nullable:$jackson_databind_nullable_version"
    {{/openApiNullable}}
    {{#withXml}}
    implementation "com.fasterxml.jackson.dataformat:jackson-dataformat-xml:$jackson_version"
    {{/withXml}}
    {{#joda}}
    implementation "com.fasterxml.jackson.datatype:jackson-datatype-joda:$jackson_version"
    {{/joda}}
    implementation "com.fasterxml.jackson.datatype:jackson-datatype-jsr310:$jackson_version"
{{/jackson}}
{{#gson}}
    implementation "io.gsonfire:gson-fire:$gson_fire_version"
    implementation 'com.google.code.gson:gson:$gson_version'
{{/gson}}
{{#joda}}
    implementation "joda-time:joda-time:$jodatime_version"
{{/joda}}
    implementation "com.squareup.okio:okio:$okio_version"
{{#useBeanValidation}}
    implementation "jakarta.validation:jakarta.validation-api:3.0.2"
{{/useBeanValidation}}
{{#performBeanValidation}}
    implementation "org.hibernate:hibernate-validator:6.0.19.Final"
{{/performBeanValidation}}
    implementation "jakarta.annotation:jakarta.annotation-api:$jakarta_annotation_version"
    testImplementation "org.junit.jupiter:junit-jupiter-api:$junit_version"
}
