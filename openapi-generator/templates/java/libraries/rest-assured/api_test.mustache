{{>licenseInfo}}

package {{package}};

{{#imports}}import {{import}};
{{/imports}}
import {{invokerPackage}}.ApiClient;
import {{apiPackage}}.{{classname}};
import io.restassured.builder.RequestSpecBuilder;
import io.restassured.filter.log.ErrorLoggingFilter;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

{{#useBeanValidation}}
import {{javaxPackage}}.validation.constraints.*;
import {{javaxPackage}}.validation.Valid;

{{/useBeanValidation}}
import static io.restassured.config.ObjectMapperConfig.objectMapperConfig;
import static io.restassured.config.RestAssuredConfig.config;
import static {{invokerPackage}}.{{#gson}}GsonObjectMapper.gson{{/gson}}{{#jackson}}JacksonObjectMapper.jackson{{/jackson}};

/**
 * API tests for {{classname}}
 */
@Disabled
public class {{classname}}Test {

    private {{classname}} api;

    @BeforeEach
    public void createApi() {
        api = ApiClient.api(ApiClient.Config.apiConfig().reqSpecSupplier(
                () -> new RequestSpecBuilder()
                        .setConfig(config().objectMapperConfig(objectMapperConfig().defaultObjectMapper({{#gson}}gson(){{/gson}}{{#jackson}}jackson(){{/jackson}})))
                        .addFilter(new ErrorLoggingFilter())
                        .setBaseUri("{{{basePath}}}"))).{{classVarName}}();
    }
{{#operations}}
{{#operation}}

    {{#responses}}
    /**
     * {{message}}
     */
    @Test
    public void shouldSee{{code}}After{{operationIdCamelCase}}() {
        {{#allParams}}
        {{#isHeaderParam}}String {{paramName}} = null;{{/isHeaderParam}}{{^isHeaderParam}}{{{dataType}}} {{paramName}} = null;{{/isHeaderParam}}
        {{/allParams}}
        api.{{operationId}}(){{#allParams}}{{#required}}{{#isPathParam}}
                .{{paramName}}Path({{paramName}}){{/isPathParam}}{{#isQueryParam}}
                .{{paramName}}Query({{paramName}}){{/isQueryParam}}{{#isFormParam}}{{^isFile}}
                .{{paramName}}Form({{paramName}}){{/isFile}}{{/isFormParam}}{{#isFormParam}}{{#isFile}}
                .{{paramName}}MultiPart({{paramName}}){{/isFile}}{{/isFormParam}}{{#isHeaderParam}}
                .{{paramName}}Header({{paramName}}){{/isHeaderParam}}{{#isBodyParam}}
                .body({{paramName}}){{/isBodyParam}}{{/required}}{{/allParams}}.execute(r -> r.prettyPeek());
        // TODO: test validations
    }

    {{/responses}}
{{/operation}}
{{/operations}}
}
