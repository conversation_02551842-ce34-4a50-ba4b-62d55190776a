{{>licenseInfo}}

package {{invokerPackage}};

import com.fasterxml.jackson.annotation.*;
import com.fasterxml.jackson.databind.*;
{{#openApiNullable}}
import org.openapitools.jackson.nullable.JsonNullableModule;
{{/openApiNullable}}
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
{{#joda}}
import com.fasterxml.jackson.datatype.joda.JodaModule;
{{/joda}}

import io.restassured.internal.mapping.Jackson2Mapper;
import io.restassured.path.json.mapper.factory.Jackson2ObjectMapperFactory;


public class JacksonObjectMapper extends Jackson2Mapper {

    private JacksonObjectMapper() {
        super(createFactory());
    }

    private static Jackson2ObjectMapperFactory createFactory() {
        return (cls, charset) -> {
            ObjectMapper mapper = new ObjectMapper();
            mapper = new ObjectMapper();
            mapper.setSerializationInclusion(JsonInclude.Include.NON_NULL);
            mapper.configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, {{failOnUnknownProperties}});
            mapper.configure(DeserializationFeature.FAIL_ON_INVALID_SUBTYPE, false);
            mapper.disable(SerializationFeature.WRITE_DATES_AS_TIMESTAMPS);
            mapper.enable(SerializationFeature.WRITE_ENUMS_USING_TO_STRING);
            mapper.enable(DeserializationFeature.READ_ENUMS_USING_TO_STRING);
            mapper.setDateFormat(new RFC3339DateFormat());
            mapper.registerModule(new JavaTimeModule());
            {{#joda}}
            mapper.registerModule(new JodaModule());
            {{/joda}}
            {{#openApiNullable}}
            JsonNullableModule jnm = new JsonNullableModule();
            mapper.registerModule(jnm);
            {{/openApiNullable}}
            return mapper;
        };
    }

    public static JacksonObjectMapper jackson() {
        return new JacksonObjectMapper();
    }
}
