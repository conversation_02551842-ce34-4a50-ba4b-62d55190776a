lazy val root = (project in file(".")).
  settings(
    organization := "{{groupId}}",
    name := "{{artifactId}}",
    version := "{{artifactVersion}}",
    scalaVersion := "2.11.12",
    scalacOptions ++= Seq("-feature"),
    compile / javacOptions ++= Seq("-Xlint:deprecation"),
    Compile / packageDoc / publishArtifact := false,
    resolvers += Resolver.mavenLocal,
    libraryDependencies ++= Seq(
      "io.swagger" % "swagger-annotations" % "1.6.6",
      "io.rest-assured" % "rest-assured" % "4.5.1",
      "io.rest-assured" % "scala-support" % "4.5.1",
      "com.google.code.findbugs" % "jsr305" % "3.0.2",
{{#jackson}}
      "com.fasterxml.jackson.core" % "jackson-core" % "2.13.4",
      "com.fasterxml.jackson.core" % "jackson-annotations" % "2.13.4",
      "com.fasterxml.jackson.core" % "jackson-databind" % "2.13.4.2",
      {{#openApiNullable}}
      "org.openapitools" % "jackson-databind-nullable" % "0.2.6",
      {{/openApiNullable}}
      {{#withXml}}
      "com.fasterxml.jackson.dataformat" % "jackson-dataformat-xml" % "2.13.4.1",
      {{/withXml}}
      {{#joda}}
      "com.fasterxml.jackson.datatype" % "jackson-datatype-joda" % "2.13.4.1",
      {{/joda}}
      "com.fasterxml.jackson.datatype" % "jackson-datatype-jsr310" % "2.13.4.1",
{{/jackson}}
{{#gson}}
      "com.google.code.gson" % "gson" % "2.8.9",
      "io.gsonfire" % "gson-fire" % "1.9.0" % "compile",
{{/gson}}
{{#joda}}
      "joda-time" % "joda-time" % "2.10.5" % "compile",
{{/joda}}
      "com.squareup.okio" % "okio" % "1.17.5" % "compile",
{{#useBeanValidation}}
      "jakarta.validation" % "jakarta.validation-api" % "3.0.2" % "compile",
{{/useBeanValidation}}
{{#performBeanValidation}}
      "org.hibernate" % "hibernate-validator" % "6.0.19.Final" % "compile",
{{/performBeanValidation}}
    "jakarta.annotation" % "jakarta.annotation-api" % "1.3.5" % "compile",
      "org.junit.jupiter" % "junit-jupiter-api" % "5.10.3" % "test",
      "com.novocode" % "junit-interface" % "0.10" % "test"
    )
  )
