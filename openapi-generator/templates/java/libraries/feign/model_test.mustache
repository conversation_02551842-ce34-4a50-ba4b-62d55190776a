{{>licenseInfo}}

package {{package}};

{{#imports}}import {{import}};
{{/imports}}
import org.junit.jupiter.api.Test;

/**
 * Model tests for {{classname}}
 */
class {{classname}}Test {
    {{#models}}
    {{#model}}
    {{^vendorExtensions.x-is-one-of-interface}}
    {{^isEnum}}
    private final {{classname}} model = new {{classname}}();

    {{/isEnum}}
    /**
     * Model tests for {{classname}}
     */
    @Test
    void test{{classname}}() {
        // TODO: test {{classname}}
    }

    {{#allVars}}
    /**
     * Test the property '{{name}}'
     */
    @Test
    void {{name}}Test() {
        // TODO: test {{name}}
    }

    {{/allVars}}
    {{/vendorExtensions.x-is-one-of-interface}}
    {{/model}}
    {{/models}}
}
