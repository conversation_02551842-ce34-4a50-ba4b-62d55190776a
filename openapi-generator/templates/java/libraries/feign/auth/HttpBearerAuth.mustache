{{>licenseInfo}}

package {{invokerPackage}}.auth;

import feign.RequestInterceptor;
import feign.RequestTemplate;
import java.util.Optional;
import java.util.function.Supplier;

/**
 * An interceptor that adds the request header needed to use HTTP bearer authentication.
 */
public class HttpBearerAuth implements RequestInterceptor {
  private final String scheme;
  private Supplier<String> tokenSupplier;

  public HttpBearerAuth(String scheme) {
    this.scheme = scheme;
  }

  /**
   * Gets the token, which together with the scheme, will be sent as the value of the Authorization header.
   *
   * @return The bearer token
   */
  public String getBearerToken() {
    return tokenSupplier.get();
  }

  /**
   * Sets the token, which together with the scheme, will be sent as the value of the Authorization header.
   *
   * @param bearerToken The bearer token to send in the Authorization header
   */
  public void setBearerToken(String bearerToken) {
    this.tokenSupplier = () -> bearerToken;
  }

  /**
   * Sets the supplier of tokens, which together with the scheme, will be sent as the value of the Authorization header.
   *
   * @param tokenSupplier The supplier of bearer tokens to send in the Authorization header
   */
  public void setBearerToken(Supplier<String> tokenSupplier) {
    this.tokenSupplier = tokenSupplier;
  }

  @Override
  public void apply(RequestTemplate template) {
    String bearerToken = Optional.ofNullable(tokenSupplier).map(Supplier::get).orElse(null);
    if (bearerToken == null) {
      return;
    }

    template.header("Authorization", (scheme != null ? upperCaseBearer(scheme) + " " : "") + bearerToken);
  }

  private static String upperCaseBearer(String scheme) {
    return ("bearer".equalsIgnoreCase(scheme)) ? "Bearer" : scheme;
  }
}
