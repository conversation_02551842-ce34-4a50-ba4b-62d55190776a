package {{package}};

import {{invokerPackage}}.ApiClient;
{{#imports}}import {{import}};
{{/imports}}
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.BeforeEach;

import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

{{#useBeanValidation}}
import {{javaxPackage}}.validation.constraints.*;
import {{javaxPackage}}.validation.Valid;

{{/useBeanValidation}}
/**
 * API tests for {{classname}}
 */
class {{classname}}Test {

    private {{classname}} api;

    @BeforeEach
    public void setup() {
        api = new ApiClient().buildClient({{classname}}.class);
    }

    {{#operations}}{{#operation}}
    /**
     * {{summary}}
     *
     * {{notes}}
     */
    @Test
    void {{operationId}}Test() {
        {{#allParams}}
        {{{dataType}}} {{paramName}} = null;
        {{/allParams}}
        // {{#returnType}}{{{.}}} response = {{/returnType}}api.{{operationId}}({{#allParams}}{{paramName}}{{^-last}}, {{/-last}}{{/allParams}});

        // TODO: test validations
    }

        {{#hasQueryParams}}
    /**
     * {{summary}}
     *
     * {{notes}}
     *
     * This tests the overload of the method that uses a Map for query parameters instead of
     * listing them out individually.
     */
    @Test
    void {{operationId}}TestQueryMap() {
          {{#allParams}}
            {{^isQueryParam}}
        {{{dataType}}} {{paramName}} = null;
            {{/isQueryParam}}
          {{/allParams}}
        {{classname}}.{{operationIdCamelCase}}QueryParams queryParams = new {{classname}}.{{operationIdCamelCase}}QueryParams()
          {{#queryParams}}
            .{{paramName}}(null){{#-last}};{{/-last}}
          {{/queryParams}}
        // {{#returnType}}{{{.}}} response = {{/returnType}}api.{{operationId}}({{#allParams}}{{^isQueryParam}}{{paramName}}, {{/isQueryParam}}{{/allParams}}queryParams);

    // TODO: test validations
    }
        {{/hasQueryParams}}
    {{/operation}}{{/operations}}
}
