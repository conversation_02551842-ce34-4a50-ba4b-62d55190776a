apply plugin: 'idea'
apply plugin: 'eclipse'

group = '{{groupId}}'
version = '{{artifactVersion}}'

buildscript {
    repositories {
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:2.3.+'
        classpath 'com.github.dcendents:android-maven-gradle-plugin:1.5'
    }
}

repositories {
    mavenCentral()
}


if(hasProperty('target') && target == 'android') {

    apply plugin: 'com.android.library'
    apply plugin: 'com.github.dcendents.android-maven'

    android {
        compileSdkVersion 25
        buildToolsVersion '25.0.2'
        defaultConfig {
            minSdkVersion 14
            targetSdkVersion 25
        }
        compileOptions {
            sourceCompatibility JavaVersion.VERSION_1_8
            targetCompatibility JavaVersion.VERSION_1_8
        }

        // Rename the aar correctly
        libraryVariants.all { variant ->
            variant.outputs.each { output ->
                def outputFile = output.outputFile
                if (outputFile != null && outputFile.name.endsWith('.aar')) {
                    def fileName = "${project.name}-${variant.baseName}-${version}.aar"
                    output.outputFile = new File(outputFile.parent, fileName)
                }
            }
        }

        dependencies {
            provided "jakarta.annotation:jakarta.annotation-api:$jakarta_annotation_version"
        }
    }

    afterEvaluate {
        android.libraryVariants.all { variant ->
            def task = project.tasks.create "jar${variant.name.capitalize()}", Jar
            task.description = "Create jar artifact for ${variant.name}"
            task.dependsOn variant.javaCompile
            task.from variant.javaCompile.destinationDirectory
            task.destinationDirectory = project.file("${project.buildDir}/outputs/jar")
            task.archiveFileName = "${project.name}-${variant.baseName}-${version}.jar"
            artifacts.add('archives', task);
        }
    }

    task sourcesJar(type: Jar) {
        from android.sourceSets.main.java.srcDirs
        classifier = 'sources'
    }

    artifacts {
        archives sourcesJar
    }

} else {

    apply plugin: 'java'
    apply plugin: 'maven-publish'

    sourceCompatibility = JavaVersion.VERSION_1_8
    targetCompatibility = JavaVersion.VERSION_1_8

    publishing {
        publications {
            maven(MavenPublication) {
                artifactId = '{{artifactId}}'
                from components.java
            }
        }
    }

    task execute(type:JavaExec) {
       main = System.getProperty('mainClass')
       classpath = sourceSets.main.runtimeClasspath
    }
}

test {
    useJUnitPlatform()
}

ext {
    swagger_annotations_version = "1.6.11"
    {{#jackson}}
    jackson_version = "2.17.1"
    jackson_databind_version = "2.17.1"
    {{/jackson}}
    {{#openApiNullable}}
    jackson_databind_nullable_version = "0.2.6"
    {{/openApiNullable}}
    jakarta_annotation_version = "1.3.5"
    feign_version = "13.5"
    feign_form_version = "3.8.0"
    junit_version = "5.7.0"
    scribejava_version = "8.0.0"
    {{#useReflectionEqualsHashCode}}
    commons_lang3_version = "3.17.0"
    {{/useReflectionEqualsHashCode}}
}

dependencies {
    implementation "io.swagger:swagger-annotations:$swagger_annotations_version"
    implementation "com.google.code.findbugs:jsr305:3.0.2"
    implementation "io.github.openfeign:feign-core:$feign_version"
    {{#jackson}}
    implementation "io.github.openfeign:feign-jackson:$feign_version"
    {{/jackson}}
    implementation "io.github.openfeign:feign-slf4j:$feign_version"
    {{#feign-okhttp}}
    implementation "io.github.openfeign:feign-okhttp:$feign_version"
    {{/feign-okhttp}}
    {{#feign-hc5}}
    implementation "io.github.openfeign:feign-hc5:$feign_version"
    {{/feign-hc5}}
    implementation "io.github.openfeign.form:feign-form:$feign_form_version"
    {{#jackson}}
    {{#joda}}
    implementation "com.fasterxml.jackson.datatype:jackson-datatype-joda:$jackson_version"
    {{/joda}}
    implementation "com.fasterxml.jackson.core:jackson-core:$jackson_version"
    implementation "com.fasterxml.jackson.core:jackson-annotations:$jackson_version"
    implementation "com.fasterxml.jackson.core:jackson-databind:$jackson_databind_version"
    implementation "com.fasterxml.jackson.datatype:jackson-datatype-jsr310:$jackson_version"
    {{/jackson}}
    {{#openApiNullable}}
    implementation "org.openapitools:jackson-databind-nullable:$jackson_databind_nullable_version"
    {{/openApiNullable}}
    implementation "com.brsanthu:migbase64:2.2"
    implementation "com.github.scribejava:scribejava-core:$scribejava_version"
    implementation "com.brsanthu:migbase64:2.2"
    implementation "jakarta.annotation:jakarta.annotation-api:$jakarta_annotation_version"
    {{#useReflectionEqualsHashCode}}
    implementation "org.apache.commons:commons-lang3:$commons_lang3_version"
    {{/useReflectionEqualsHashCode}}
    testImplementation "org.junit.jupiter:junit-jupiter:$junit_version"
    testRuntimeOnly "org.junit.jupiter:junit-jupiter-engine:$junit_version"
    testImplementation "org.junit.jupiter:junit-jupiter-params:$junit_version"
    testImplementation "com.github.tomakehurst:wiremock-jre8:2.35.1"
    testImplementation "org.hamcrest:hamcrest:2.2"
    testImplementation "commons-io:commons-io:2.16.1"
    testImplementation "ch.qos.logback:logback-classic:1.2.3"
}
