/**
 * {{description}}{{^description}}{{classname}}{{/description}}{{#isDeprecated}}
 * @deprecated{{/isDeprecated}}
 */{{#isDeprecated}}
@Deprecated{{/isDeprecated}}
{{#swagger1AnnotationLibrary}}
{{#description}}
@ApiModel(description = "{{{.}}}")
{{/description}}
{{/swagger1AnnotationLibrary}}
{{#swagger2AnnotationLibrary}}
{{#description}}
@Schema(description = "{{{.}}}")
{{/description}}
{{/swagger2AnnotationLibrary}}
{{#jackson}}
@JsonPropertyOrder({
{{#vars}}
  {{classname}}.JSON_PROPERTY_{{nameInSnakeCase}}{{^-last}},{{/-last}}
{{/vars}}
})
{{#isClassnameSanitized}}
@JsonTypeName("{{name}}")
{{/isClassnameSanitized}}
{{/jackson}}
{{>additionalModelTypeAnnotations}}{{#discriminator}}{{>typeInfoAnnotation}}{{/discriminator}}{{>xmlAnnotation}}
{{#vendorExtensions.x-class-extra-annotation}}
{{{vendorExtensions.x-class-extra-annotation}}}
{{/vendorExtensions.x-class-extra-annotation}}
public class {{classname}} {{#parent}}extends {{{.}}} {{/parent}}{{#vendorExtensions.x-implements}}{{#-first}}implements {{{.}}}{{/-first}}{{^-first}}, {{{.}}}{{/-first}}{{#-last}} {{/-last}}{{/vendorExtensions.x-implements}}{
{{#serializableModel}}
  private static final long serialVersionUID = 1L;

{{/serializableModel}}
  {{#vars}}
    {{#isEnum}}
    {{^isContainer}}
    {{^vendorExtensions.x-enum-as-string}}
{{>modelInnerEnum}}
    {{/vendorExtensions.x-enum-as-string}}
    {{/isContainer}}
    {{#isContainer}}
    {{#mostInnerItems}}
{{>modelInnerEnum}}
    {{/mostInnerItems}}
    {{/isContainer}}
    {{/isEnum}}
  {{#gson}}
  public static final String SERIALIZED_NAME_{{nameInSnakeCase}} = "{{baseName}}";
  {{/gson}}
  {{#jackson}}
  public static final String JSON_PROPERTY_{{nameInSnakeCase}} = "{{baseName}}";
  {{/jackson}}
  {{#withXml}}
  @Xml{{#isXmlAttribute}}Attribute{{/isXmlAttribute}}{{^isXmlAttribute}}Element{{/isXmlAttribute}}(name = "{{items.xmlName}}{{^items.xmlName}}{{xmlName}}{{^xmlName}}{{baseName}}{{/xmlName}}{{/items.xmlName}}"{{#xmlNamespace}}, namespace = "{{.}}"{{/xmlNamespace}})
    {{#isXmlWrapped}}
  @XmlElementWrapper(name = "{{xmlName}}{{^xmlName}}{{baseName}}{{/xmlName}}"{{#xmlNamespace}}, namespace = "{{.}}"{{/xmlNamespace}})
    {{/isXmlWrapped}}
  {{/withXml}}
  {{#gson}}
  @SerializedName(SERIALIZED_NAME_{{nameInSnakeCase}})
  {{/gson}}
  {{#vendorExtensions.x-field-extra-annotation}}
  {{{vendorExtensions.x-field-extra-annotation}}}
  {{/vendorExtensions.x-field-extra-annotation}}
  {{#vendorExtensions.x-is-jackson-optional-nullable}}
  {{#isContainer}}
  {{#deprecated}}
  @Deprecated
  {{/deprecated}}
  private JsonNullable<{{{datatypeWithEnum}}}> {{name}} = JsonNullable.<{{{datatypeWithEnum}}}>undefined();
  {{/isContainer}}
  {{^isContainer}}
  {{#deprecated}}
  @Deprecated
  {{/deprecated}}
  private JsonNullable<{{{datatypeWithEnum}}}> {{name}} = JsonNullable.<{{{datatypeWithEnum}}}>{{#defaultValue}}of({{{.}}}){{/defaultValue}}{{^defaultValue}}undefined(){{/defaultValue}};
  {{/isContainer}}
  {{/vendorExtensions.x-is-jackson-optional-nullable}}
  {{^vendorExtensions.x-is-jackson-optional-nullable}}
  {{#deprecated}}
  @Deprecated
  {{/deprecated}}
  {{>nullable_var_annotations}}
  private {{{datatypeWithEnum}}} {{name}}{{#defaultValue}} = {{{.}}}{{/defaultValue}};
  {{/vendorExtensions.x-is-jackson-optional-nullable}}

  {{/vars}}
  public {{classname}}() { {{#parent}}{{#parcelableModel}}
    super();{{/parcelableModel}}{{/parent}}{{#gson}}{{#discriminator}}
    this.{{{discriminatorName}}} = this.getClass().getSimpleName();{{/discriminator}}{{/gson}}
  }{{#vendorExtensions.x-has-readonly-properties}}{{^withXml}}{{#jackson}}

  @JsonCreator
  public {{classname}}(
  {{#readOnlyVars}}
    {{#jackson}}@JsonProperty(JSON_PROPERTY_{{nameInSnakeCase}}){{/jackson}} {{{datatypeWithEnum}}} {{name}}{{^-last}}, {{/-last}}
  {{/readOnlyVars}}
  ) {
    this();
  {{#readOnlyVars}}
    this.{{name}} = {{#vendorExtensions.x-is-jackson-optional-nullable}}{{name}} == null ? JsonNullable.<{{{datatypeWithEnum}}}>undefined() : JsonNullable.of({{name}}){{/vendorExtensions.x-is-jackson-optional-nullable}}{{^vendorExtensions.x-is-jackson-optional-nullable}}{{name}}{{/vendorExtensions.x-is-jackson-optional-nullable}};
  {{/readOnlyVars}}
  }{{/jackson}}{{/withXml}}{{/vendorExtensions.x-has-readonly-properties}}
  {{#vars}}

  {{^isReadOnly}}
  {{#vendorExtensions.x-enum-as-string}}
  public static final Set<String> {{{nameInSnakeCase}}}_VALUES = new HashSet<>(Arrays.asList(
    {{#allowableValues}}{{#enumVars}}{{{value}}}{{^-last}}, {{/-last}}{{/enumVars}}{{/allowableValues}}
  ));

  {{/vendorExtensions.x-enum-as-string}}
  {{#deprecated}}
  @Deprecated
  {{/deprecated}}
  public {{classname}} {{name}}({{>nullable_var_annotations}} {{{datatypeWithEnum}}} {{name}}) {
    {{#vendorExtensions.x-enum-as-string}}
    if (!{{{nameInSnakeCase}}}_VALUES.contains({{name}})) {
      throw new IllegalArgumentException({{name}} + " is invalid. Possible values for {{name}}: " + String.join(", ", {{{nameInSnakeCase}}}_VALUES));
    }

    {{/vendorExtensions.x-enum-as-string}}
    {{#vendorExtensions.x-is-jackson-optional-nullable}}
    this.{{name}} = JsonNullable.<{{{datatypeWithEnum}}}>of({{name}});
    {{/vendorExtensions.x-is-jackson-optional-nullable}}
    {{^vendorExtensions.x-is-jackson-optional-nullable}}
    this.{{name}} = {{name}};
    {{/vendorExtensions.x-is-jackson-optional-nullable}}
    return this;
  }
  {{#isArray}}

  public {{classname}} add{{nameInPascalCase}}Item({{{items.datatypeWithEnum}}} {{name}}Item) {
    {{#vendorExtensions.x-is-jackson-optional-nullable}}
    if (this.{{name}} == null || !this.{{name}}.isPresent()) {
      this.{{name}} = JsonNullable.<{{{datatypeWithEnum}}}>of({{{defaultValue}}}{{^defaultValue}}new {{#uniqueItems}}LinkedHashSet{{/uniqueItems}}{{^uniqueItems}}ArrayList{{/uniqueItems}}<>(){{/defaultValue}});
    }
    try {
      this.{{name}}.get().add({{name}}Item);
    } catch (java.util.NoSuchElementException e) {
      // this can never happen, as we make sure above that the value is present
    }
    return this;
    {{/vendorExtensions.x-is-jackson-optional-nullable}}
    {{^vendorExtensions.x-is-jackson-optional-nullable}}
    if (this.{{name}} == null) {
      this.{{name}} = {{{defaultValue}}}{{^defaultValue}}new {{#uniqueItems}}LinkedHashSet{{/uniqueItems}}{{^uniqueItems}}ArrayList{{/uniqueItems}}<>(){{/defaultValue}};
    }
    this.{{name}}.add({{name}}Item);
    return this;
    {{/vendorExtensions.x-is-jackson-optional-nullable}}
  }
  {{/isArray}}
  {{#isMap}}

  public {{classname}} put{{nameInPascalCase}}Item(String key, {{{items.datatypeWithEnum}}} {{name}}Item) {
    {{#vendorExtensions.x-is-jackson-optional-nullable}}
    if (this.{{name}} == null || !this.{{name}}.isPresent()) {
      this.{{name}} = JsonNullable.<{{{datatypeWithEnum}}}>of({{{defaultValue}}}{{^defaultValue}}new HashMap<>(){{/defaultValue}});
    }
    try {
      this.{{name}}.get().put(key, {{name}}Item);
    } catch (java.util.NoSuchElementException e) {
      // this can never happen, as we make sure above that the value is present
    }
    return this;
    {{/vendorExtensions.x-is-jackson-optional-nullable}}
    {{^vendorExtensions.x-is-jackson-optional-nullable}}
    if (this.{{name}} == null) {
      this.{{name}} = {{{defaultValue}}}{{^defaultValue}}new HashMap<>(){{/defaultValue}};
    }
    this.{{name}}.put(key, {{name}}Item);
    return this;
    {{/vendorExtensions.x-is-jackson-optional-nullable}}
  }
  {{/isMap}}

  {{/isReadOnly}}
  /**
  {{#description}}
   * {{.}}
  {{/description}}
  {{^description}}
   * Get {{name}}
  {{/description}}
  {{#minimum}}
   * minimum: {{.}}
  {{/minimum}}
  {{#maximum}}
   * maximum: {{.}}
  {{/maximum}}
   * @return {{name}}
  {{#deprecated}}
   * @deprecated
  {{/deprecated}}
   */
{{#deprecated}}
  @Deprecated
{{/deprecated}}
  {{>nullable_var_annotations}}
{{#useBeanValidation}}
{{>beanValidation}}
{{/useBeanValidation}}
{{#swagger1AnnotationLibrary}}
  @ApiModelProperty({{#example}}example = "{{{.}}}", {{/example}}{{#required}}required = {{required}}, {{/required}}value = "{{{description}}}")
{{/swagger1AnnotationLibrary}}
{{#swagger2AnnotationLibrary}}
  @Schema({{#example}}example = "{{{.}}}", {{/example}}requiredMode = {{#required}}Schema.RequiredMode.REQUIRED{{/required}}{{^required}}Schema.RequiredMode.NOT_REQUIRED{{/required}}, description = "{{{description}}}")
{{/swagger2AnnotationLibrary}}
{{#vendorExtensions.x-extra-annotation}}
  {{{vendorExtensions.x-extra-annotation}}}
{{/vendorExtensions.x-extra-annotation}}
{{#vendorExtensions.x-is-jackson-optional-nullable}}
  {{!unannotated, Jackson would pick this up automatically and add it *in addition* to the _JsonNullable getter field}}
  @JsonIgnore
{{/vendorExtensions.x-is-jackson-optional-nullable}}
{{^vendorExtensions.x-is-jackson-optional-nullable}}{{#jackson}}{{> jackson_annotations}}{{/jackson}}{{/vendorExtensions.x-is-jackson-optional-nullable}}
  public {{{datatypeWithEnum}}} {{getter}}() {
    {{#vendorExtensions.x-is-jackson-optional-nullable}}
    {{#isReadOnly}}{{! A readonly attribute doesn't have setter => jackson will set null directly if explicitly returned by API, so make sure we have an empty JsonNullable}}
    if ({{name}} == null) {
      {{name}} = JsonNullable.<{{{datatypeWithEnum}}}>{{#defaultValue}}of({{{.}}}){{/defaultValue}}{{^defaultValue}}undefined(){{/defaultValue}};
    }
    {{/isReadOnly}}
    return {{name}}.orElse(null);
    {{/vendorExtensions.x-is-jackson-optional-nullable}}
    {{^vendorExtensions.x-is-jackson-optional-nullable}}
    return {{name}};
    {{/vendorExtensions.x-is-jackson-optional-nullable}}
  }

  {{#vendorExtensions.x-is-jackson-optional-nullable}}
{{> jackson_annotations}}
  public JsonNullable<{{{datatypeWithEnum}}}> {{getter}}_JsonNullable() {
    return {{name}};
  }
  {{/vendorExtensions.x-is-jackson-optional-nullable}}{{#vendorExtensions.x-is-jackson-optional-nullable}}
  @JsonProperty(JSON_PROPERTY_{{nameInSnakeCase}})
  {{#isReadOnly}}private{{/isReadOnly}}{{^isReadOnly}}public{{/isReadOnly}} void {{setter}}_JsonNullable(JsonNullable<{{{datatypeWithEnum}}}> {{name}}) {
    {{! For getters/setters that have name differing from attribute name, we must include setter (albeit private) for jackson to be able to set the attribute}}
    this.{{name}} = {{name}};
  }
  {{/vendorExtensions.x-is-jackson-optional-nullable}}

  {{^isReadOnly}}
  {{#deprecated}}
  @Deprecated
  {{/deprecated}}
{{#vendorExtensions.x-setter-extra-annotation}}  {{{vendorExtensions.x-setter-extra-annotation}}}
{{/vendorExtensions.x-setter-extra-annotation}}{{#jackson}}{{^vendorExtensions.x-is-jackson-optional-nullable}}{{> jackson_annotations}}{{/vendorExtensions.x-is-jackson-optional-nullable}}{{/jackson}}  public void {{setter}}({{>nullable_var_annotations}} {{{datatypeWithEnum}}} {{name}}) {
    {{#vendorExtensions.x-enum-as-string}}
    if (!{{{nameInSnakeCase}}}_VALUES.contains({{name}})) {
      throw new IllegalArgumentException({{name}} + " is invalid. Possible values for {{name}}: " + String.join(", ", {{{nameInSnakeCase}}}_VALUES));
    }

    {{/vendorExtensions.x-enum-as-string}}
    {{#vendorExtensions.x-is-jackson-optional-nullable}}
    this.{{name}} = JsonNullable.<{{{datatypeWithEnum}}}>of({{name}});
    {{/vendorExtensions.x-is-jackson-optional-nullable}}
    {{^vendorExtensions.x-is-jackson-optional-nullable}}
    this.{{name}} = {{name}};
    {{/vendorExtensions.x-is-jackson-optional-nullable}}
  }
  {{/isReadOnly}}

  {{/vars}}
{{>libraries/jersey2/additional_properties}}
  /**
   * Return true if this {{name}} object is equal to o.
   */
  @Override
  public boolean equals(Object o) {
  {{#useReflectionEqualsHashCode}}
    return EqualsBuilder.reflectionEquals(this, o, false, null, true);
  {{/useReflectionEqualsHashCode}}
  {{^useReflectionEqualsHashCode}}
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }{{#hasVars}}
    {{classname}} {{classVarName}} = ({{classname}}) o;
    return {{#vars}}{{#vendorExtensions.x-is-jackson-optional-nullable}}equalsNullable(this.{{name}}, {{classVarName}}.{{name}}){{/vendorExtensions.x-is-jackson-optional-nullable}}{{^vendorExtensions.x-is-jackson-optional-nullable}}{{#isByteArray}}Arrays{{/isByteArray}}{{^isByteArray}}Objects{{/isByteArray}}.equals(this.{{name}}, {{classVarName}}.{{name}}){{/vendorExtensions.x-is-jackson-optional-nullable}}{{^-last}} &&
        {{/-last}}{{/vars}}{{#additionalPropertiesType}}&&
        Objects.equals(this.additionalProperties, {{classVarName}}.additionalProperties){{/additionalPropertiesType}}{{#parent}} &&
        super.equals(o){{/parent}};{{/hasVars}}{{^hasVars}}
    return {{#parent}}super.equals(o){{/parent}}{{^parent}}true{{/parent}};{{/hasVars}}
  {{/useReflectionEqualsHashCode}}
  }{{#vendorExtensions.x-jackson-optional-nullable-helpers}}

  private static <T> boolean equalsNullable(JsonNullable<T> a, JsonNullable<T> b) {
    return a == b || (a != null && b != null && a.isPresent() && b.isPresent() && Objects.deepEquals(a.get(), b.get()));
  }{{/vendorExtensions.x-jackson-optional-nullable-helpers}}

  @Override
  public int hashCode() {
  {{#useReflectionEqualsHashCode}}
    return HashCodeBuilder.reflectionHashCode(this);
  {{/useReflectionEqualsHashCode}}
  {{^useReflectionEqualsHashCode}}
    return Objects.hash({{#vars}}{{#vendorExtensions.x-is-jackson-optional-nullable}}hashCodeNullable({{name}}){{/vendorExtensions.x-is-jackson-optional-nullable}}{{^vendorExtensions.x-is-jackson-optional-nullable}}{{^isByteArray}}{{name}}{{/isByteArray}}{{#isByteArray}}Arrays.hashCode({{name}}){{/isByteArray}}{{/vendorExtensions.x-is-jackson-optional-nullable}}{{^-last}}, {{/-last}}{{/vars}}{{#parent}}{{#hasVars}}, {{/hasVars}}super.hashCode(){{/parent}}{{#additionalPropertiesType}}{{#hasVars}}, {{/hasVars}}{{^hasVars}}{{#parent}}, {{/parent}}{{/hasVars}}additionalProperties{{/additionalPropertiesType}});
  {{/useReflectionEqualsHashCode}}
  }{{#vendorExtensions.x-jackson-optional-nullable-helpers}}

  private static <T> int hashCodeNullable(JsonNullable<T> a) {
    if (a == null) {
      return 1;
    }
    return a.isPresent() ? Arrays.deepHashCode(new Object[]{a.get()}) : 31;
  }{{/vendorExtensions.x-jackson-optional-nullable-helpers}}

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class {{classname}} {\n");
    {{#parent}}
    sb.append("    ").append(toIndentedString(super.toString())).append("\n");
    {{/parent}}
    {{#vars}}
    sb.append("    {{name}}: ").append({{#isPassword}}"*"{{/isPassword}}{{^isPassword}}toIndentedString({{name}}){{/isPassword}}).append("\n");
    {{/vars}}
    {{#additionalPropertiesType}}
    sb.append("    additionalProperties: ").append(toIndentedString(additionalProperties)).append("\n");
    {{/additionalPropertiesType}}
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }

{{#parcelableModel}}

  public void writeToParcel(Parcel out, int flags) {
{{#model}}
{{#isArray}}
    out.writeList(this);
{{/isArray}}
{{^isArray}}
{{#parent}}
    super.writeToParcel(out, flags);
{{/parent}}
{{#vars}}
    out.writeValue({{name}});
{{/vars}}
{{/isArray}}
{{/model}}
  }

  {{classname}}(Parcel in) {
{{#isArray}}
    in.readTypedList(this, {{arrayModelType}}.CREATOR);
{{/isArray}}
{{^isArray}}
{{#parent}}
    super(in);
{{/parent}}
{{#vars}}
{{#isPrimitiveType}}
    {{name}} = ({{{datatypeWithEnum}}})in.readValue(null);
{{/isPrimitiveType}}
{{^isPrimitiveType}}
    {{name}} = ({{{datatypeWithEnum}}})in.readValue({{complexType}}.class.getClassLoader());
{{/isPrimitiveType}}
{{/vars}}
{{/isArray}}
  }

  public int describeContents() {
    return 0;
  }

  public static final Parcelable.Creator<{{classname}}> CREATOR = new Parcelable.Creator<>() {
    public {{classname}} createFromParcel(Parcel in) {
{{#model}}
{{#isArray}}
      {{classname}} result = new {{classname}}();
      result.addAll(in.readArrayList({{arrayModelType}}.class.getClassLoader()));
      return result;
{{/isArray}}
{{^isArray}}
      return new {{classname}}(in);
{{/isArray}}
{{/model}}
    }
    public {{classname}}[] newArray(int size) {
      return new {{classname}}[size];
    }
  };
{{/parcelableModel}}
{{#discriminator}}
  static {
    // Initialize and register the discriminator mappings.
    Map<String, Class<?>> mappings = new HashMap<>();
    {{#mappedModels}}
    mappings.put("{{mappingName}}", {{modelName}}.class);
    {{/mappedModels}}
    mappings.put("{{name}}", {{classname}}.class);
    JSON.registerDiscriminator({{classname}}.class, "{{propertyBaseName}}", mappings);
  }
{{/discriminator}}
}
