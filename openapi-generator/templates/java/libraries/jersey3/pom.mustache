<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>{{groupId}}</groupId>
    <artifactId>{{artifactId}}</artifactId>
    <packaging>jar</packaging>
    <name>{{artifactId}}</name>
    <version>{{artifactVersion}}</version>
    <url>{{artifactUrl}}</url>
    <description>{{artifactDescription}}</description>
    <scm>
        <connection>{{scmConnection}}</connection>
        <developerConnection>{{scmDeveloperConnection}}</developerConnection>
        <url>{{scmUrl}}</url>
    </scm>
{{#parentOverridden}}
    <parent>
        <groupId>{{{parentGroupId}}}</groupId>
        <artifactId>{{{parentArtifactId}}}</artifactId>
        <version>{{{parentVersion}}}</version>
    </parent>
{{/parentOverridden}}

    <licenses>
        <license>
            <name>{{licenseName}}</name>
            <url>{{licenseUrl}}</url>
            <distribution>repo</distribution>
        </license>
    </licenses>

    <developers>
        <developer>
            <name>{{developerName}}</name>
            <email>{{developerEmail}}</email>
            <organization>{{developerOrganization}}</organization>
            <organizationUrl>{{developerOrganizationUrl}}</organizationUrl>
        </developer>
    </developers>

    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-enforcer-plugin</artifactId>
                <version>3.0.0</version>
                <executions>
                    <execution>
                        <id>enforce-maven</id>
                        <goals>
                            <goal>enforce</goal>
                        </goals>
                        <configuration>
                            <rules>
                                <requireMavenVersion>
                                    <version>2.2.0</version>
                                </requireMavenVersion>
                            </rules>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>3.0.0-M5</version>
                <configuration>
                    <systemPropertyVariables>
                        <property>
                            <name>loggerPath</name>
                            <value>conf/log4j.properties</value>
                        </property>
                    </systemPropertyVariables>
                    <argLine>-Xms512m -Xmx1500m</argLine>
                    <parallel>methods</parallel>
                    <threadCount>10</threadCount>
                    <trimStackTrace>false</trimStackTrace>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-dependency-plugin</artifactId>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/lib</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!-- attach test jar -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.2.2</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>test-jar</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.codehaus.mojo</groupId>
                <artifactId>build-helper-maven-plugin</artifactId>
                <version>3.3.0</version>
                <executions>
                    <execution>
                        <id>add_sources</id>
                        <phase>generate-sources</phase>
                        <goals>
                            <goal>add-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>src/main/java</source>
                            </sources>
                        </configuration>
                    </execution>
                    <execution>
                        <id>add_test_sources</id>
                        <phase>generate-test-sources</phase>
                        <goals>
                            <goal>add-test-source</goal>
                        </goals>
                        <configuration>
                            <sources>
                                <source>src/test/java</source>
                            </sources>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.10.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                    <fork>true</fork>
                    <meminitial>128m</meminitial>
                    <maxmem>512m</maxmem>
                    <compilerArgs>
                        <arg>-Xlint:all</arg>
                        <arg>-J-Xss4m</arg><!-- Compiling the generated JSON.java file may require larger stack size. -->
                    </compilerArgs>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>3.3.2</version>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <doclint>none</doclint>
                    <source>1.8</source>
                    <tags>
                        <tag>
                            <name>http.response.details</name>
                            <placement>a</placement>
                            <head>Http Response Details:</head>
                        </tag>
                    </tags>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-source-plugin</artifactId>
                <version>3.2.0</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar-no-fork</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <!-- Use spotless plugin to automatically format code, remove unused import, etc
                 To apply changes directly to the file, run `mvn spotless:apply`
                 Ref: https://github.com/diffplug/spotless/tree/main/plugin-maven
            -->
            <plugin>
              <groupId>com.diffplug.spotless</groupId>
              <artifactId>spotless-maven-plugin</artifactId>
              <version>${spotless.version}</version>
              <configuration>
                <formats>
                  <!-- you can define as many formats as you want, each is independent -->
                  <format>
                    <!-- define the files to apply to -->
                    <includes>
                      <include>.gitignore</include>
                    </includes>
                    <!-- define the steps to apply to those files -->
                    <trimTrailingWhitespace/>
                    <endWithNewline/>
                    <indent>
                      <spaces>true</spaces> <!-- or <tabs>true</tabs> -->
                      <spacesPerTab>4</spacesPerTab> <!-- optional, default is 4 -->
                    </indent>
                  </format>
                </formats>
                <!-- define a language-specific format -->
                <java>
                  <!-- no need to specify files, inferred automatically, but you can if you want -->

                  <!-- apply a specific flavor of google-java-format and reflow long strings -->
                  <googleJavaFormat>
                    <version>1.8</version>
                    <style>AOSP</style>
                    <reflowLongStrings>true</reflowLongStrings>
                  </googleJavaFormat>
                  <removeUnusedImports/>
                  <importOrder/>
                </java>
              </configuration>
            </plugin>
        </plugins>
    </build>

    <profiles>
        <profile>
            <id>sign-artifacts</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>3.0.1</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <dependencies>
        {{#swagger1AnnotationLibrary}}
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>${swagger-annotations-version}</version>
        </dependency>
        {{/swagger1AnnotationLibrary}}
        {{#swagger2AnnotationLibrary}}
        <dependency>
            <groupId>io.swagger.core.v3</groupId>
            <artifactId>swagger-annotations</artifactId>
            <version>${swagger-annotations-version}</version>
        </dependency>
        {{/swagger2AnnotationLibrary}}

        <!-- @Nullable annotation -->
        <dependency>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
            <version>3.0.2</version>
        </dependency>

        <!-- HTTP client: jersey-client -->
        <dependency>
            <groupId>org.glassfish.jersey.core</groupId>
            <artifactId>jersey-client</artifactId>
            <version>${jersey-version}</version>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jersey.inject</groupId>
            <artifactId>jersey-hk2</artifactId>
            <version>${jersey-version}</version>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jersey.media</groupId>
            <artifactId>jersey-media-multipart</artifactId>
            <version>${jersey-version}</version>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jersey.media</groupId>
            <artifactId>jersey-media-json-jackson</artifactId>
            <version>${jersey-version}</version>
        </dependency>

        <!-- JSON processing: jackson -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>${jackson-version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>${jackson-version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson-databind-version}</version>
        </dependency>
        {{#openApiNullable}}
        <dependency>
            <groupId>org.openapitools</groupId>
            <artifactId>jackson-databind-nullable</artifactId>
            <version>${jackson-databind-nullable-version}</version>
        </dependency>
        {{/openApiNullable}}
        {{#withXml}}
        <!-- XML processing: JAXB -->
        <dependency>
            <groupId>org.glassfish.jersey.media</groupId>
            <artifactId>jersey-media-jaxb</artifactId>
            <version>${jersey-version}</version>
        </dependency>
        {{/withXml}}
        {{#joda}}
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-joda</artifactId>
            <version>${jackson-version}</version>
        </dependency>
        {{/joda}}
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jsr310</artifactId>
            <version>${jackson-version}</version>
        </dependency>
        {{#hasHttpSignatureMethods}}
        <dependency>
            <groupId>org.tomitribe</groupId>
            <artifactId>tomitribe-http-signatures</artifactId>
            <version>${http-signature-version}</version>
        </dependency>
        {{/hasHttpSignatureMethods}}
        {{#hasOAuthMethods}}
        <dependency>
            <groupId>com.github.scribejava</groupId>
            <artifactId>scribejava-apis</artifactId>
            <version>${scribejava-apis-version}</version>
        </dependency>
        {{/hasOAuthMethods}}
        {{#useBeanValidation}}
        <!-- Bean Validation API support -->
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
            <version>${beanvalidation-version}</version>
            <scope>provided</scope>
        </dependency>
        {{/useBeanValidation}}
        <dependency>
            <groupId>jakarta.annotation</groupId>
            <artifactId>jakarta.annotation-api</artifactId>
            <version>${jakarta-annotation-version}</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>org.glassfish.jersey.connectors</groupId>
            <artifactId>jersey-apache-connector</artifactId>
            <version>${jersey-version}</version>
        </dependency>
        {{#useReflectionEqualsHashCode}}
        <!-- For equals and hashCode using reflection -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${commons-lang3-version}</version>
        </dependency>
        {{/useReflectionEqualsHashCode}}

        <!-- test dependencies -->
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <version>${junit-version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        {{#swagger1AnnotationLibrary}}
        <swagger-annotations-version>1.6.6</swagger-annotations-version>
        {{/swagger1AnnotationLibrary}}
        {{#swagger2AnnotationLibrary}}
        <swagger-annotations-version>2.2.15</swagger-annotations-version>
        {{/swagger2AnnotationLibrary}}
        <jersey-version>3.1.1</jersey-version>
        <jackson-version>2.17.1</jackson-version>
        <jackson-databind-version>2.17.1</jackson-databind-version>
        <jackson-databind-nullable-version>0.2.6</jackson-databind-nullable-version>
        <jakarta-annotation-version>2.1.1</jakarta-annotation-version>
        <beanvalidation-version>3.0.2</beanvalidation-version>
        <junit-version>5.10.0</junit-version>
        {{#hasHttpSignatureMethods}}
        <http-signature-version>1.8</http-signature-version>
        {{/hasHttpSignatureMethods}}
        {{#hasOAuthMethods}}
        <scribejava-apis-version>8.3.3</scribejava-apis-version>
        {{/hasOAuthMethods}}
        {{#useReflectionEqualsHashCode}}
        <commons-lang3-version>3.17.0</commons-lang3-version>
        {{/useReflectionEqualsHashCode}}
        <spotless.version>2.21.0</spotless.version>
    </properties>
</project>
