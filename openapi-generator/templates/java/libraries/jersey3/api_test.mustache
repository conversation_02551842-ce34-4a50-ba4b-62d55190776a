{{>licenseInfo}}

package {{package}};

import {{invokerPackage}}.*;
import {{invokerPackage}}.auth.*;
{{#imports}}import {{import}};
{{/imports}}

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

{{#useBeanValidation}}
import jakarta.validation.constraints.*;
import jakarta.validation.Valid;

{{/useBeanValidation}}
/**
 * API tests for {{classname}}
 */
public class {{classname}}Test {

    private final {{classname}} api = new {{classname}}();

    {{#operations}}
    {{#operation}}
    /**
     {{#summary}}
     * {{summary}}
     *
     {{/summary}}
     {{#notes}}
     * {{notes}}
     *
     {{/notes}}
     * @throws ApiException if the Api call fails
     */
    @Test
    public void {{operationId}}Test() throws ApiException {
        {{#allParams}}
        //{{{dataType}}} {{paramName}} = null;
        {{/allParams}}
        {{^vendorExtensions.x-group-parameters}}
        //{{#returnType}}{{{.}}} response = {{/returnType}}api.{{operationId}}({{#allParams}}{{paramName}}{{^-last}}, {{/-last}}{{/allParams}});
        {{/vendorExtensions.x-group-parameters}}
        {{#vendorExtensions.x-group-parameters}}
        //{{#returnType}}{{{.}}} response = {{/returnType}}api.{{operationId}}({{#pathParams}}{{paramName}}{{^-last}}, {{/-last}}{{/pathParams}}){{#allParams}}{{^isPathParam}}
        //        .{{paramName}}({{paramName}}){{/isPathParam}}{{/allParams}}
        //        .execute();
        {{/vendorExtensions.x-group-parameters}}
        // TODO: test validations
    }

    {{/operation}}
    {{/operations}}
}
