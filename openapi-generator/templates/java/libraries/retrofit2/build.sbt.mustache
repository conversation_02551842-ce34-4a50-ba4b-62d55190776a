lazy val root = (project in file(".")).
  settings(
    organization := "{{groupId}}",
    name := "{{artifactId}}",
    version := "{{artifactVersion}}",
    scalaVersion := "2.11.12",
    scalacOptions ++= Seq("-feature"),
    compile / javacOptions ++= Seq("-Xlint:deprecation"),
    Compile / packageDoc / publishArtifact := false,
    resolvers += Resolver.mavenLocal,
    libraryDependencies ++= Seq(
      "com.squareup.retrofit2" % "retrofit" % "2.11.0" % "compile",
      "com.squareup.retrofit2" % "converter-scalars" % "2.11.0" % "compile",
      {{^usePlayWS}}
      "com.squareup.retrofit2" % "converter-gson" % "2.11.0" % "compile",
      {{/usePlayWS}}
      {{#usePlayWS}}
      "com.typesafe.play" % "play-ahc-ws_2.12" % "2.6.7" % "compile",
      "jakarta.validation" % "jakarta.validation-api" % "3.0.2" % "compile",
      "com.squareup.retrofit2" % "converter-jackson" % "2.11.0" % "compile",
      "com.fasterxml.jackson.core" % "jackson-core" % "2.17.1" % "compile",
      "com.fasterxml.jackson.core" % "jackson-annotations" % "2.17.1" % "compile",
      "com.fasterxml.jackson.core" % "jackson-databind" % "2.17.1" % "compile",
      {{/usePlayWS}}
      {{#useRxJava2}}
      "com.squareup.retrofit2" % "adapter-rxjava2" % "2.11.0" % "compile",
      "io.reactivex.rxjava2" % "rxjava" % "2.1.1" % "compile",
      {{/useRxJava2}}
      {{#useRxJava3}}
      "com.squareup.retrofit2" % "adapter-rxjava3" % "2.11.0" % "compile",
      "io.reactivex.rxjava3" % "rxjava" % "3.0.4" % "compile",
      {{/useRxJava3}}
      "io.swagger" % "swagger-annotations" % "1.5.21" % "compile",
      "org.apache.oltu.oauth2" % "org.apache.oltu.oauth2.client" % "1.0.1" % "compile",
      {{#joda}}
      "joda-time" % "joda-time" % "2.9.9" % "compile",
      {{/joda}}
      "io.gsonfire" % "gson-fire" % "1.9.0" % "compile",
      "jakarta.annotation" % "jakarta.annotation-api" % "1.3.5" % "compile",
      "org.junit.jupiter" % "junit-jupiter-api" % "5.10.3" % "test",
      "com.novocode" % "junit-interface" % "0.11" % "test"
    )
  )
