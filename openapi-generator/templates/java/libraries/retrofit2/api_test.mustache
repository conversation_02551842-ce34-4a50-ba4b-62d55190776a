package {{package}};

import {{invokerPackage}}.ApiClient;
{{#imports}}import {{import}};
{{/imports}}
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

{{#useBeanValidation}}
import {{javaxPackage}}.validation.constraints.*;
import {{javaxPackage}}.validation.Valid;

{{/useBeanValidation}}
/**
 * API tests for {{classname}}
 */
public class {{classname}}Test {

    private {{classname}} api;

    @BeforeEach
    public void setup() {
        api = new ApiClient().createService({{classname}}.class);
    }

    {{#operations}}
    {{#operation}}
    /**
     * {{summary}}
     *
     * {{notes}}
     */
    @Test
    public void {{operationId}}Test() {
        {{#allParams}}
        {{{dataType}}} {{paramName}} = null;
        {{/allParams}}
        // {{#returnType}}{{{.}}} response = {{/returnType}}api.{{operationId}}({{#allParams}}{{paramName}}{{^-last}}, {{/-last}}{{/allParams}});

        // TODO: test validations
    }
    {{/operation}}
    {{/operations}}
}
