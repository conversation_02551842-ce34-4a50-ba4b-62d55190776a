package {{package}};

import {{invokerPackage}}.CollectionFormats.*;

{{#useRxJava2}}
import io.reactivex.Observable;
{{/useRxJava2}}
{{#doNotUseRx}}
import retrofit2.Call;
{{/doNotUseRx}}
import retrofit2.http.*;

import okhttp3.RequestBody;
import okhttp3.ResponseBody;
import okhttp3.MultipartBody;

{{#imports}}import {{import}};
{{/imports}}

{{#useBeanValidation}}
import {{javaxPackage}}.validation.constraints.*;
import {{javaxPackage}}.validation.Valid;
{{/useBeanValidation}}

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import java.util.concurrent.*;
import retrofit2.Response;

{{#operations}}
public interface {{classname}} {
  {{#operation}}
  /**
   * {{summary}}
   * {{notes}}
{{#allParams}}
   * @param {{paramName}} {{description}}{{#required}} (required){{/required}}{{^required}} (optional{{#defaultValue}}, default to {{.}}{{/defaultValue}}){{/required}}
{{/allParams}}
   * @return Call&lt;{{returnType}}{{^returnType}}Void{{/returnType}}&gt;
{{#isDeprecated}}
   * @deprecated
{{/isDeprecated}}
   */
  {{#isDeprecated}}
  @Deprecated
  {{/isDeprecated}}
  {{#formParams}}
  {{#-first}}
  {{#isMultipart}}@retrofit2.http.Multipart{{/isMultipart}}{{^isMultipart}}@retrofit2.http.FormUrlEncoded{{/isMultipart}}
  {{/-first}}
  {{/formParams}}
  {{^formParams}}
  {{#prioritizedContentTypes}}
  {{#-first}}
  @Headers({
    "Content-Type:{{{mediaType}}}"
  })
  {{/-first}}
  {{/prioritizedContentTypes}}
  {{/formParams}}
  @{{httpMethod}}("{{{path}}}")
  CompletionStage<Response<{{{returnType}}}{{^returnType}}Void{{/returnType}}>> {{operationId}}({{^allParams}});{{/allParams}}
    {{#allParams}}{{>libraries/retrofit2/queryParams}}{{>libraries/retrofit2/pathParams}}{{>libraries/retrofit2/headerParams}}{{>libraries/retrofit2/bodyParams}}{{>libraries/retrofit2/formParams}}{{^-last}}, {{/-last}}{{#-last}}
  );{{/-last}}{{/allParams}}

  {{/operation}}
}
{{/operations}}
