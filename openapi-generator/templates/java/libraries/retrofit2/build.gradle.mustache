apply plugin: 'idea'
apply plugin: 'eclipse'

group = '{{groupId}}'
version = '{{artifactVersion}}'

buildscript {
    repositories {
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:2.3.+'
        classpath 'com.github.dcendents:android-maven-gradle-plugin:1.5'
    }
}

repositories {
    mavenCentral()
}


if(hasProperty('target') && target == 'android') {

    apply plugin: 'com.android.library'
    apply plugin: 'com.github.dcendents.android-maven'

    android {
        compileSdkVersion 25
        buildToolsVersion '25.0.2'
        defaultConfig {
            minSdkVersion 14
            targetSdkVersion 25
        }
        compileOptions {
            sourceCompatibility JavaVersion.VERSION_1_8
            targetCompatibility JavaVersion.VERSION_1_8
        }

        // Rename the aar correctly
        libraryVariants.all { variant ->
            variant.outputs.each { output ->
                def outputFile = output.outputFile
                if (outputFile != null && outputFile.name.endsWith('.aar')) {
                    def fileName = "${project.name}-${variant.baseName}-${version}.aar"
                    output.outputFile = new File(outputFile.parent, fileName)
                }
            }
        }

        dependencies {
            provided "jakarta.annotation:jakarta.annotation-api:$jakarta_annotation_version"
        }
    }

    afterEvaluate {
        android.libraryVariants.all { variant ->
            def task = project.tasks.create "jar${variant.name.capitalize()}", Jar
            task.description = "Create jar artifact for ${variant.name}"
            task.dependsOn variant.javaCompile
            task.from variant.javaCompile.destinationDirectory
            task.destinationDirectory = project.file("${project.buildDir}/outputs/jar")
            task.archiveFileName = "${project.name}-${variant.baseName}-${version}.jar"
            artifacts.add('archives', task);
        }
    }

    task sourcesJar(type: Jar) {
        from android.sourceSets.main.java.srcDirs
        classifier = 'sources'
    }

    artifacts {
        archives sourcesJar
    }

} else {

    apply plugin: 'java'
    apply plugin: 'maven-publish'

    sourceCompatibility = JavaVersion.VERSION_1_8
    targetCompatibility = JavaVersion.VERSION_1_8

    publishing {
        publications {
            maven(MavenPublication) {
               artifactId = '{{artifactId}}'
               from components.java
            }
        }
    }

    task execute(type:JavaExec) {
       main = System.getProperty('mainClass')
       classpath = sourceSets.main.runtimeClasspath
    }
}

ext {
    oltu_version = "1.0.1"
    retrofit_version = "2.11.0"
    {{#jackson}}
    jackson_version = "2.17.1"
    jackson_databind_version = "2.17.1"
    javax_ws_rs_api_version = "2.1.1"
        {{#openApiNullable}}
    jackson_databind_nullable_version = "0.2.6"
        {{/openApiNullable}}
    {{/jackson}}
    {{#usePlayWS}}
    play_version = "2.6.7"
    {{/usePlayWS}}
    jakarta_annotation_version = "1.3.5"
    swagger_annotations_version = "1.5.22"
    junit_version = "5.10.3"
    {{#useRxJava2}}
    rx_java_version = "2.1.1"
    {{/useRxJava2}}
    {{#useRxJava3}}
    rx_java_version = "3.0.4"
    {{/useRxJava3}}
    {{#joda}}
    jodatime_version = "2.9.9"
    {{/joda}}
    json_fire_version = "1.9.0"
}

dependencies {
    implementation "com.squareup.retrofit2:retrofit:$retrofit_version"
    implementation "com.squareup.retrofit2:converter-scalars:$retrofit_version"
    implementation "com.squareup.retrofit2:converter-gson:$retrofit_version"
    {{#useRxJava2}}
    implementation 'com.squareup.retrofit2:adapter-rxjava2:2.3.0'
    implementation "io.reactivex.rxjava2:rxjava:$rx_java_version"
    {{/useRxJava2}}
    {{#useRxJava3}}
    implementation 'com.squareup.retrofit2:adapter-rxjava3:$$retrofit_version'
    implementation "io.reactivex.rxjava3:rxjava:$rx_java_version"
    {{/useRxJava3}}
    implementation "io.swagger:swagger-annotations:$swagger_annotations_version"
    implementation "com.google.code.findbugs:jsr305:3.0.2"
    implementation ("org.apache.oltu.oauth2:org.apache.oltu.oauth2.client:$oltu_version"){
        exclude group:'org.apache.oltu.oauth2' , module: 'org.apache.oltu.oauth2.common'
    }
    implementation "io.gsonfire:gson-fire:$json_fire_version"
    {{#joda}}
    implementation "joda-time:joda-time:$jodatime_version"
    {{/joda}}
    {{#usePlayWS}}
    implementation "com.typesafe.play:play-ahc-ws_2.12:$play_version"
    {{/usePlayWS}}
    {{#jackson}}
    implementation "jakarta.validation:jakarta.validation-api:3.0.2"
    implementation "com.squareup.retrofit2:converter-jackson:$retrofit_version"
    implementation "com.fasterxml.jackson.core:jackson-core:$jackson_version"
    implementation "com.fasterxml.jackson.core:jackson-annotations:$jackson_version"
    implementation "com.fasterxml.jackson.core:jackson-databind:$jackson_databind_version"
    implementation "javax.ws.rs:javax.ws.rs-api:$javax_ws_rs_api_version"
    {{#openApiNullable}}
    implementation "org.openapitools:jackson-databind-nullable:$jackson_databind_nullable_version"
    {{/openApiNullable}}
    implementation "com.fasterxml.jackson.datatype:jackson-datatype-jsr310:$jackson_version"
    {{/jackson}}
    implementation "jakarta.annotation:jakarta.annotation-api:$jakarta_annotation_version"
    testImplementation "org.junit.jupiter:junit-jupiter-api:$junit_version"
}
