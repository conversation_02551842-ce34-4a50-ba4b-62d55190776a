{{>licenseInfo}}

package {{package}};

{{#imports}}import {{import}};
{{/imports}}
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for {{classname}}
 */
public class {{classname}}Test {

    private final {{classname}} api = new {{classname}}();

    {{#operations}}
    {{#operation}}
    /**
     * {{summary}}
     *
     * {{notes}}
     *
     * @throws IOException
     *          if the Api call fails
     */
    @Test
    public void {{operationId}}Test() throws IOException {
        {{#allParams}}
        {{{dataType}}} {{paramName}} = null;
        {{/allParams}}
        // uncomment below to test the API function
        //{{#returnType}}{{{.}}} response = {{/returnType}}api.{{operationId}}({{#allParams}}{{paramName}}{{^-last}}, {{/-last}}{{/allParams}});

        // TODO: test validations
    }

    {{/operation}}
    {{/operations}}
}
