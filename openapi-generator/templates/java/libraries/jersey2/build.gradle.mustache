apply plugin: 'idea'
apply plugin: 'eclipse'
apply plugin: 'com.diffplug.spotless'

group = '{{groupId}}'
version = '{{artifactVersion}}'

buildscript {
    repositories {
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:2.3.+'
        classpath 'com.github.dcendents:android-maven-gradle-plugin:2.1'
        classpath 'com.diffplug.spotless:spotless-plugin-gradle:6.3.0'
    }
}

repositories {
    mavenCentral()
}

if(hasProperty('target') && target == 'android') {

    apply plugin: 'com.android.library'
    apply plugin: 'com.github.dcendents.android-maven'

    android {
        compileSdkVersion 25
        buildToolsVersion '25.0.2'
        defaultConfig {
            minSdkVersion 14
            targetSdkVersion 25
        }
        compileOptions {
            sourceCompatibility JavaVersion.VERSION_1_8
            targetCompatibility JavaVersion.VERSION_1_8
        }

        // Rename the aar correctly
        libraryVariants.all { variant ->
            variant.outputs.each { output ->
                def outputFile = output.outputFile
                if (outputFile != null && outputFile.name.endsWith('.aar')) {
                    def fileName = "${project.name}-${variant.baseName}-${version}.aar"
                    output.outputFile = new File(outputFile.parent, fileName)
                }
            }
        }

        dependencies {
            provided "jakarta.annotation:jakarta.annotation-api:$jakarta_annotation_version"
        }
    }

    afterEvaluate {
        android.libraryVariants.all { variant ->
            def task = project.tasks.create "jar${variant.name.capitalize()}", Jar
            task.description = "Create jar artifact for ${variant.name}"
            task.dependsOn variant.javaCompile
            task.from variant.javaCompile.destinationDirectory
            task.destinationDirectory = project.file("${project.buildDir}/outputs/jar")
            task.archiveFileName = "${project.name}-${variant.baseName}-${version}.jar"
            artifacts.add('archives', task);
        }
    }

    task sourcesJar(type: Jar) {
        from android.sourceSets.main.java.srcDirs
        classifier = 'sources'
    }

    artifacts {
        archives sourcesJar
    }

} else {

    apply plugin: 'java'
    apply plugin: 'maven-publish'
    sourceCompatibility = JavaVersion.VERSION_1_8
    targetCompatibility = JavaVersion.VERSION_1_8

    publishing {
        publications {
            maven(MavenPublication) {
               artifactId = '{{artifactId}}'

               from components.java
            }
        }
    }

    task execute(type:JavaExec) {
       main = System.getProperty('mainClass')
       classpath = sourceSets.main.runtimeClasspath
    }
}

ext {
    swagger_annotations_version = "1.6.5"
    jackson_version = "2.17.1"
    jackson_databind_version = "2.17.1"
    {{#openApiNullable}}
    jackson_databind_nullable_version = "0.2.6"
    {{/openApiNullable}}
    jakarta_annotation_version = "1.3.5"
    {{#useBeanValidation}}
    bean_validation_version = "3.0.2"
    {{/useBeanValidation}}
    jersey_version = "2.35"
    junit_version = "5.8.2"
    {{#hasOAuthMethods}}
    scribejava_apis_version = "8.3.1"
    {{/hasOAuthMethods}}
    {{#hasHttpSignatureMethods}}
    tomitribe_http_signatures_version = "1.7"
    {{/hasHttpSignatureMethods}}
    {{#useReflectionEqualsHashCode}}
    commons_lang3_version = "3.17.0"
    {{/useReflectionEqualsHashCode}}
}

dependencies {
    implementation "io.swagger:swagger-annotations:$swagger_annotations_version"
    implementation "com.google.code.findbugs:jsr305:3.0.2"
    implementation "org.glassfish.jersey.core:jersey-client:$jersey_version"
    implementation "org.glassfish.jersey.inject:jersey-hk2:$jersey_version"
    implementation "org.glassfish.jersey.media:jersey-media-multipart:$jersey_version"
    implementation "org.glassfish.jersey.media:jersey-media-json-jackson:$jersey_version"
    implementation "org.glassfish.jersey.connectors:jersey-apache-connector:$jersey_version"
    implementation "com.fasterxml.jackson.core:jackson-core:$jackson_version"
    implementation "com.fasterxml.jackson.core:jackson-annotations:$jackson_version"
    implementation "com.fasterxml.jackson.core:jackson-databind:$jackson_databind_version"
    {{#openApiNullable}}
    implementation "org.openapitools:jackson-databind-nullable:$jackson_databind_nullable_version"
    {{/openApiNullable}}
    {{#joda}}
    implementation "com.fasterxml.jackson.datatype:jackson-datatype-joda:$jackson_version"
    {{/joda}}
    implementation "com.fasterxml.jackson.datatype:jackson-datatype-jsr310:$jackson_version"
    {{#hasOAuthMethods}}
    implementation "com.github.scribejava:scribejava-apis:$scribejava_apis_version"
    {{/hasOAuthMethods}}
    {{#hasHttpSignatureMethods}}
    implementation "org.tomitribe:tomitribe-http-signatures:$tomitribe_http_signatures_version"
    {{/hasHttpSignatureMethods}}
    implementation "jakarta.annotation:jakarta.annotation-api:$jakarta_annotation_version"
    {{#useBeanValidation}}
    implementation "jakarta.validation:jakarta.validation-api:$bean_validation_version"
    {{/useBeanValidation}}
    {{#useReflectionEqualsHashCode}}
    implementation "org.apache.commons:commons-lang3:$commons_lang3_version"
    {{/useReflectionEqualsHashCode}}
    testImplementation "org.junit.jupiter:junit-jupiter-api:$junit_version"
    testRuntimeOnly "org.junit.jupiter:junit-jupiter-engine:$junit_version"
}

test {
    useJUnitPlatform()
}

javadoc {
    options.tags = [ "http.response.details:a:Http Response Details" ]
}

// Use spotless plugin to automatically format code, remove unused import, etc
// To apply changes directly to the file, run `gradlew spotlessApply`
// Ref: https://github.com/diffplug/spotless/tree/main/plugin-gradle
spotless {
    // comment out below to run spotless as part of the `check` task
    enforceCheck false

    format 'misc', {
        // define the files (e.g. '*.gradle', '*.md') to apply `misc` to
        target '.gitignore'
        // define the steps to apply to those files
        trimTrailingWhitespace()
        indentWithSpaces() // Takes an integer argument if you don't like 4
        endWithNewline()
    }
    java {
        // don't need to set target, it is inferred from java
        // apply a specific flavor of google-java-format
        googleJavaFormat('1.8').aosp().reflowLongStrings()
        removeUnusedImports()
        importOrder()
    }
}
