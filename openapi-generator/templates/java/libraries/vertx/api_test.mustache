{{>licenseInfo}}
package {{package}};

{{#imports}}import {{import}};
{{/imports}}

import {{invokerPackage}}.Configuration;

import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import io.vertx.core.AsyncResult;
import io.vertx.core.Handler;
import io.vertx.core.json.JsonObject;
import io.vertx.core.Vertx;
import io.vertx.ext.unit.junit.VertxUnitRunner;
import io.vertx.ext.unit.junit.RunTestOnContext;
import io.vertx.ext.unit.TestContext;
import io.vertx.ext.unit.Async;

import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API tests for {{classname}}
 */
@Disabled
public class {{classname}}Test {

    private {{classname}} api;

    @BeforeAll
    public void setupApiClient() {
        api = new {{classname}}Impl();
    }

    {{#operations}}
    {{#operation}}
    /**
     * {{summary}}
     * {{notes}}
     *
     * @param context Vertx test context for doing assertions
     */
    @Test
    public void {{operationId}}Test(TestContext testContext) {
        Async async = testContext.async();
        {{#allParams}}
        {{{dataType}}} {{paramName}} = null;
        {{/allParams}}
        api.{{operationId}}({{#allParams}}{{paramName}}, {{/allParams}}result -> {
            // TODO: test validations
            async.complete();
        });
    }
    {{/operation}}
    {{/operations}}
}
