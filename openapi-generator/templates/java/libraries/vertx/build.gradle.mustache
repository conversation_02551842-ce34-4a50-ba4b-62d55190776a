apply plugin: 'idea'
apply plugin: 'eclipse'

group = '{{groupId}}'
version = '{{artifactVersion}}'

repositories {
    mavenCentral()
}

apply plugin: 'java'
apply plugin: 'maven-publish'

sourceCompatibility = JavaVersion.VERSION_1_8
targetCompatibility = JavaVersion.VERSION_1_8

publishing {
    publications {
        maven(MavenPublication) {
            artifactId = '{{artifactId}}'
            from components.java
        }
    }
}

task execute(type:JavaExec) {
   main = System.getProperty('mainClass')
   classpath = sourceSets.main.runtimeClasspath
}

ext {
    swagger_annotations_version = "1.5.21"
    jackson_version = "2.17.1"
    jackson_databind_version = "2.17.1"
    vertx_version = "{{#supportVertxFuture}}4.0.0{{/supportVertxFuture}}{{^supportVertxFuture}}3.5.2{{/supportVertxFuture}}"
    junit_version = "5.10.3"
    {{#openApiNullable}}
    jackson_databind_nullable_version = "0.2.6"
    {{/openApiNullable}}
    jakarta_annotation_version = "1.3.5"
}

dependencies {
    implementation "io.swagger:swagger-annotations:$swagger_annotations_version"
    implementation "com.google.code.findbugs:jsr305:3.0.2"
    implementation "io.vertx:vertx-web-client:$vertx_version"
    implementation "io.vertx:vertx-rx-java:$vertx_version"
    implementation "com.fasterxml.jackson.core:jackson-core:$jackson_version"
    implementation "com.fasterxml.jackson.core:jackson-annotations:$jackson_version"
    implementation "com.fasterxml.jackson.core:jackson-databind:$jackson_databind_version"
    {{#joda}}
    implementation "com.fasterxml.jackson.datatype:jackson-datatype-joda:$jackson_version"
    {{/joda}}
    implementation "com.fasterxml.jackson.datatype:jackson-datatype-jsr310:$jackson_version"
    {{#openApiNullable}}
    implementation "org.openapitools:jackson-databind-nullable:$jackson_databind_nullable_version"
    {{/openApiNullable}}
    implementation "jakarta.annotation:jakarta.annotation-api:$jakarta_annotation_version"
    testImplementation "org.junit.jupiter:junit-jupiter-api:$junit_version"
    testImplementation "io.vertx:vertx-unit:$vertx_version"
}
