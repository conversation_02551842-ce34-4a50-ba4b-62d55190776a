# {{artifactId}}

{{appName}}

- API version: {{appVersion}}
{{^hideGenerationTimestamp}}

- Build date: {{generatedDate}}
{{/hideGenerationTimestamp}}

- Generator version: {{generatorVersion}}

{{{appDescriptionWithNewLines}}}

{{#infoUrl}}
  For more information, please visit [{{{infoUrl}}}]({{{infoUrl}}})
{{/infoUrl}}

*Automatically generated by the [OpenAPI Generator](https://openapi-generator.tech)*

## Requirements

Building the API client library requires:

1. Java 17+
2. Maven/Gradle

## Installation

To install the API client library to your local Maven repository, simply execute:

```shell
mvn clean install
```

To deploy it to a remote Maven repository instead, configure the settings of the repository and execute:

```shell
mvn clean deploy
```

Refer to the [OSSRH Guide](http://central.sonatype.org/pages/ossrh-guide.html) for more information.

### Maven users

Add this dependency to your project's POM:

```xml
<dependency>
  <groupId>{{{groupId}}}</groupId>
  <artifactId>{{{artifactId}}}</artifactId>
  <version>{{{artifactVersion}}}</version>
  <scope>compile</scope>
</dependency>
```

### Gradle users

Add this dependency to your project's build file:

```groovy
  repositories {
    mavenCentral()     // Needed if the '{{{artifactId}}}' jar has been published to maven central.
    mavenLocal()       // Needed if the '{{{artifactId}}}' jar has been published to the local maven repo.
  }

  dependencies {
     implementation "{{{groupId}}}:{{{artifactId}}}:{{{artifactVersion}}}"
  }
```

### Others

At first generate the JAR by executing:

```shell
mvn clean package
```

Then manually install the following JARs:

- `target/{{{artifactId}}}-{{{artifactVersion}}}.jar`
- `target/lib/*.jar`

## Getting Started

Please follow the [installation](#installation) instruction and execute the following Java code:

```java
{{#apiInfo}}{{#apis}}{{#-first}}{{#operations}}{{#operation}}{{#-first}}
import {{{invokerPackage}}}.*;
import {{{invokerPackage}}}.auth.*;
import {{{modelPackage}}}.*;
import {{{package}}}.{{{classname}}};

public class {{{classname}}}Example {

    public static void main(String[] args) {
        ApiClient defaultClient = new ApiClient();
        defaultClient.setBasePath("{{{basePath}}}");
        {{#hasAuthMethods}}{{#authMethods}}{{#isBasic}}{{#isBasicBasic}}
        // Configure HTTP basic authorization: {{{name}}}
        HttpBasicAuth {{{name}}} = (HttpBasicAuth) defaultClient.getAuthentication("{{{name}}}");
        {{{name}}}.setUsername("YOUR USERNAME");
        {{{name}}}.setPassword("YOUR PASSWORD");{{/isBasicBasic}}{{#isBasicBearer}}
        // Configure HTTP bearer authorization: {{{name}}}
        HttpBearerAuth {{{name}}} = (HttpBearerAuth) defaultClient.getAuthentication("{{{name}}}");
        {{{name}}}.setBearerToken("BEARER TOKEN");{{/isBasicBearer}}{{/isBasic}}{{#isApiKey}}
        // Configure API key authorization: {{{name}}}
        ApiKeyAuth {{{name}}} = (ApiKeyAuth) defaultClient.getAuthentication("{{{name}}}");
        {{{name}}}.setApiKey("YOUR API KEY");
        // Uncomment the following line to set a prefix for the API key, e.g. "Token" (defaults to null)
        //{{{name}}}.setApiKeyPrefix("Token");{{/isApiKey}}{{#isOAuth}}
        // Configure OAuth2 access token for authorization: {{{name}}}
        OAuth {{{name}}} = (OAuth) defaultClient.getAuthentication("{{{name}}}");
        {{{name}}}.setAccessToken("YOUR ACCESS TOKEN");{{/isOAuth}}{{#isHttpSignature}}
        // Configure HTTP signature authorization: {{{name}}}
        HttpSignatureAuth {{{name}}} = (HttpSignatureAuth) defaultClient.getAuthentication("{{{name}}}");
        // All the HTTP signature parameters below should be customized to your environment.
        // Configure the keyId
        {{{name}}}.setKeyId("YOUR KEY ID");
        // Configure the signature algorithm
        {{{name}}}.setSigningAlgorithm(SigningAlgorithm.HS2019);
        // Configure the specific cryptographic algorithm
        {{{name}}}.setAlgorithm(Algorithm.ECDSA_SHA256);
        // Configure the cryptographic algorithm parameters, if applicable
        {{{name}}}.setAlgorithmParameterSpec(null);
        // Set the cryptographic digest algorithm.
        {{{name}}}.setDigestAlgorithm("SHA-256");
        // Set the HTTP headers that should be included in the HTTP signature.
        {{{name}}}.setHeaders(Arrays.asList("date", "host"));
        // Set the private key used to sign the HTTP messages
        {{{name}}}.setPrivateKey();{{/isHttpSignature}}
        {{/authMethods}}
        {{/hasAuthMethods}}

        {{{classname}}} apiInstance = new {{{classname}}}(defaultClient);
        {{#allParams}}
        {{{dataType}}} {{{paramName}}} = {{{example}}}; // {{{dataType}}} | {{{description}}}
        {{/allParams}}
        try {
            {{#returnType}}{{{.}}} result = {{/returnType}}apiInstance.{{{operationId}}}({{#allParams}}{{{paramName}}}{{^-last}}, {{/-last}}{{/allParams}});{{#returnType}}
            System.out.println(result);{{/returnType}}
        } catch (HttpStatusCodeException e) {
            System.err.println("Exception when calling {{{classname}}}#{{{operationId}}}");
            System.err.println("Status code: " + e.getStatusCode().value());
            System.err.println("Reason: " + e.getResponseBodyAsString());
            System.err.println("Response headers: " + e.getResponseHeaders());
            e.printStackTrace();
        }
    }
}
{{/-first}}{{/operation}}{{/operations}}{{/-first}}{{/apis}}{{/apiInfo}}
```

## Documentation for API Endpoints

All URIs are relative to *{{basePath}}*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------
{{#apiInfo}}{{#apis}}{{#operations}}{{#operation}}*{{classname}}* | [**{{operationId}}**]({{apiDocPath}}{{classname}}.md#{{operationId}}) | **{{httpMethod}}** {{commonPath}}{{path}} | {{summary}}
{{/operation}}{{/operations}}{{/apis}}{{/apiInfo}}

## Documentation for Models

{{#models}}{{#model}} - [{{classname}}]({{modelDocPath}}{{classname}}.md)
{{/model}}{{/models}}

<a id="documentation-for-authorization"></a>
## Documentation for Authorization

{{^authMethods}}Endpoints do not require authorization.{{/authMethods}}
{{#hasAuthMethods}}Authentication schemes defined for the API:{{/hasAuthMethods}}
{{#authMethods}}
<a id="{{name}}"></a>
### {{name}}

{{#isApiKey}}

- **Type**: API key
- **API key parameter name**: {{keyParamName}}
- **Location**: {{#isKeyInQuery}}URL query string{{/isKeyInQuery}}{{#isKeyInHeader}}HTTP header{{/isKeyInHeader}}
{{/isApiKey}}
{{#isBasicBasic}}

- **Type**: HTTP basic authentication
{{/isBasicBasic}}
{{#isBasicBearer}}

- **Type**: HTTP Bearer Token authentication{{#bearerFormat}} ({{{.}}}){{/bearerFormat}}
{{/isBasicBearer}}
{{#isHttpSignature}}

- **Type**: HTTP signature authentication
{{/isHttpSignature}}
{{#isOAuth}}

- **Type**: OAuth
- **Flow**: {{flow}}
- **Authorization URL**: {{authorizationUrl}}
- **Scopes**: {{^scopes}}N/A{{/scopes}}
{{#scopes}}  - {{scope}}: {{description}}
{{/scopes}}
{{/isOAuth}}

{{/authMethods}}

## Recommendation

It's recommended to create an instance of `ApiClient` per thread in a multithreaded environment to avoid any potential issues.

## Author

{{#apiInfo}}{{#apis}}{{#-last}}{{infoEmail}}
{{/-last}}{{/apis}}{{/apiInfo}}
