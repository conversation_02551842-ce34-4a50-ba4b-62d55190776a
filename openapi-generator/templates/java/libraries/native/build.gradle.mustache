apply plugin: 'idea'
apply plugin: 'eclipse'
apply plugin: 'com.diffplug.spotless'

group = '{{groupId}}'
version = '{{artifactVersion}}'

buildscript {
    repositories {
        mavenCentral()
    }
    dependencies {
        classpath 'com.diffplug.spotless:spotless-plugin-gradle:6.11.0'
    }
}

repositories {
    mavenCentral()
}

apply plugin: 'java'
apply plugin: 'maven-publish'

sourceCompatibility = JavaVersion.VERSION_11
targetCompatibility = JavaVersion.VERSION_11

// Some text from the schema is copy pasted into the source files as UTF-8
// but the default still seems to be to use platform encoding
tasks.withType(JavaCompile) {
    configure(options) {
        options.encoding = 'UTF-8'
    }
}
javadoc {
    options.encoding = 'UTF-8'
}

publishing {
    publications {
        maven(MavenPublication) {
            artifactId = '{{artifactId}}'
            from components.java
        }
    }
}

task execute(type:JavaExec) {
   main = System.getProperty('mainClass')
   classpath = sourceSets.main.runtimeClasspath
}

task sourcesJar(type: Jar, dependsOn: classes) {
    archiveClassifier = 'sources'
    from sourceSets.main.allSource
}

task javadocJar(type: Jar, dependsOn: javadoc) {
    archiveClassifier = 'javadoc'
    from javadoc.destinationDir
}

artifacts {
    archives sourcesJar
    archives javadocJar
}


ext {
    {{#swagger1AnnotationLibrary}}
    swagger_annotations_version = "1.6.9"
    {{/swagger1AnnotationLibrary}}
    {{#swagger2AnnotationLibrary}}
    swagger_annotations_version = "2.2.9"
    {{/swagger2AnnotationLibrary}}
    jackson_version = "2.17.1"
    {{#useJakartaEe}}
    jakarta_annotation_version = "2.1.1"
    beanvalidation_version = "3.0.2"
    {{/useJakartaEe}}
    {{^useJakartaEe}}
    jakarta_annotation_version = "1.3.5"
    beanvalidation_version = "2.0.2"
    {{/useJakartaEe}}
    junit_version = "5.10.2"
    {{#hasFormParamsInSpec}}
    httpmime_version = "4.5.13"
    {{/hasFormParamsInSpec}}
    {{#useReflectionEqualsHashCode}}
    commons_lang3_version = "3.17.0"
    {{/useReflectionEqualsHashCode}}
}

dependencies {
    {{#swagger1AnnotationLibrary}}
    implementation "io.swagger:swagger-annotations:$swagger_annotations_version"
    {{/swagger1AnnotationLibrary}}
    {{#swagger2AnnotationLibrary}}
    implementation "io.swagger.core.v3:swagger-annotations:$swagger_annotations_version"
    {{/swagger2AnnotationLibrary}}
    implementation "com.google.code.findbugs:jsr305:3.0.2"
    implementation "com.fasterxml.jackson.core:jackson-core:$jackson_version"
    implementation "com.fasterxml.jackson.core:jackson-annotations:$jackson_version"
    implementation "com.fasterxml.jackson.core:jackson-databind:$jackson_version"
    implementation "com.fasterxml.jackson.datatype:jackson-datatype-jsr310:$jackson_version"
    implementation "org.openapitools:jackson-databind-nullable:0.2.1"
    implementation "jakarta.annotation:jakarta.annotation-api:$jakarta_annotation_version"
    {{#useBeanValidation}}
    implementation "jakarta.validation:jakarta.validation-api:$beanvalidation_version"
    {{/useBeanValidation}}
    {{#hasFormParamsInSpec}}
    implementation "org.apache.httpcomponents:httpmime:$httpmime_version"
    {{/hasFormParamsInSpec}}
    {{#useReflectionEqualsHashCode}}
    implementation "org.apache.commons:commons-lang3:$commons_lang3_version"
    {{/useReflectionEqualsHashCode}}
    testImplementation "org.junit.jupiter:junit-jupiter-api:$junit_version"
}

// Use spotless plugin to automatically format code, remove unused import, etc
// To apply changes directly to the file, run `gradlew spotlessApply`
// Ref: https://github.com/diffplug/spotless/tree/main/plugin-gradle
spotless {
    // comment out below to run spotless as part of the `check` task
    enforceCheck false
    format 'misc', {
        // define the files (e.g. '*.gradle', '*.md') to apply `misc` to
        target '.gitignore'
        // define the steps to apply to those files
        trimTrailingWhitespace()
        indentWithSpaces() // Takes an integer argument if you don't like 4
        endWithNewline()
    }
    java {
        // don't need to set target, it is inferred from java
        // apply a specific flavor of google-java-format
        googleJavaFormat('1.8').aosp().reflowLongStrings()
        removeUnusedImports()
        importOrder()
    }
}
