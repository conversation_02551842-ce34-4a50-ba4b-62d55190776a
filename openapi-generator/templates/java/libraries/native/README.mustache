# {{artifactId}}

{{appName}}

- API version: {{appVersion}}
{{^hideGenerationTimestamp}}

- Build date: {{generatedDate}}
{{/hideGenerationTimestamp}}

- Generator version: {{generatorVersion}}

{{{appDescriptionWithNewLines}}}

{{#infoUrl}}
  For more information, please visit [{{{infoUrl}}}]({{{infoUrl}}})
{{/infoUrl}}

*Automatically generated by the [OpenAPI Generator](https://openapi-generator.tech)*

## Requirements

Building the API client library requires:

1. Java 11+
2. Maven/Gradle

## Installation

To install the API client library to your local Maven repository, simply execute:

```shell
mvn clean install
```

To deploy it to a remote Maven repository instead, configure the settings of the repository and execute:

```shell
mvn clean deploy
```

Refer to the [OSSRH Guide](http://central.sonatype.org/pages/ossrh-guide.html) for more information.

### Maven users

Add this dependency to your project's POM:

```xml
<dependency>
  <groupId>{{{groupId}}}</groupId>
  <artifactId>{{{artifactId}}}</artifactId>
  <version>{{{artifactVersion}}}</version>
  <scope>compile</scope>
</dependency>
```

### Gradle users

Add this dependency to your project's build file:

```groovy
compile "{{{groupId}}}:{{{artifactId}}}:{{{artifactVersion}}}"
```

### Others

At first generate the JAR by executing:

```shell
mvn clean package
```

Then manually install the following JARs:

- `target/{{{artifactId}}}-{{{artifactVersion}}}.jar`
- `target/lib/*.jar`

## Getting Started

Please follow the [installation](#installation) instruction and execute the following Java code:

```java
{{#apiInfo}}{{#apis}}{{#-first}}{{#operations}}{{#operation}}{{#-first}}
import {{{invokerPackage}}}.*;
import {{{modelPackage}}}.*;
import {{{package}}}.{{{classname}}};{{#vendorExtensions.x-group-parameters}}
import {{{package}}}.{{{classname}}}.*;{{/vendorExtensions.x-group-parameters}}
{{#asyncNative}}
import java.util.concurrent.CompletableFuture;
{{/asyncNative}}

public class {{{classname}}}Example {

    public static void main(String[] args) {
        ApiClient defaultClient = Configuration.getDefaultApiClient();
        // Configure clients using the `defaultClient` object, such as
        // overriding the host and port, timeout, etc.
        {{{classname}}} apiInstance = new {{{classname}}}(defaultClient);
        {{#allParams}}
        {{{dataType}}} {{{paramName}}} = {{{example}}}; // {{{dataType}}} | {{{description}}}
        {{/allParams}}
        try {
            {{^vendorExtensions.x-group-parameters}}
            {{#returnType}}{{#asyncNative}}CompletableFuture<{{{returnType}}}>{{/asyncNative}}{{^asyncNative}}{{{returnType}}}{{/asyncNative}} result = {{/returnType}}{{^returnType}}{{#asyncNative}}CompletableFuture<Void> result = {{/asyncNative}}{{/returnType}}apiInstance.{{{operationId}}}({{#allParams}}{{{paramName}}}{{^-last}}, {{/-last}}{{/allParams}});
            {{/vendorExtensions.x-group-parameters}}
            {{#vendorExtensions.x-group-parameters}}
            {{#hasParams}}
            API{{operationId}}Request request = API{{operationId}}Request.newBuilder(){{#allParams}}
                .{{paramName}}({{paramName}}){{/allParams}}
                .build();
            {{/hasParams}}
            {{#returnType}}{{#asyncNative}}CompletableFuture<{{{returnType}}}>{{/asyncNative}}{{^asyncNative}}{{{returnType}}}{{/asyncNative}} result = {{/returnType}}{{^returnType}}{{#asyncNative}}CompletableFuture<Void> result = {{/asyncNative}}{{/returnType}}apiInstance.{{operationId}}({{#hasParams}}request{{/hasParams}});
            {{/vendorExtensions.x-group-parameters}}
            {{#returnType}}
            System.out.println(result{{#asyncNative}}.get(){{/asyncNative}});
            {{/returnType}}
        } catch (ApiException e) {
            System.err.println("Exception when calling {{{classname}}}#{{{operationId}}}");
            System.err.println("Status code: " + e.getCode());
            System.err.println("Reason: " + e.getResponseBody());
            System.err.println("Response headers: " + e.getResponseHeaders());
            e.printStackTrace();
        }
    }
}
{{/-first}}{{/operation}}{{/operations}}{{/-first}}{{/apis}}{{/apiInfo}}
```

## Documentation for API Endpoints

All URIs are relative to *{{basePath}}*

Class | Method | HTTP request | Description
------------ | ------------- | ------------- | -------------
{{#apiInfo}}{{#apis}}{{#operations}}{{#operation}}*{{classname}}* | [**{{operationId}}**]({{apiDocPath}}{{classname}}.md#{{operationId}}) | **{{httpMethod}}** {{path}} | {{summary}}
*{{classname}}* | [**{{operationId}}WithHttpInfo**]({{apiDocPath}}{{classname}}.md#{{operationId}}WithHttpInfo) | **{{httpMethod}}** {{path}} | {{summary}}
{{/operation}}{{/operations}}{{/apis}}{{/apiInfo}}

## Documentation for Models

{{#models}}{{#model}} - [{{classname}}]({{modelDocPath}}{{classname}}.md)
{{/model}}{{/models}}

<a id="documentation-for-authorization"></a>
## Documentation for Authorization

{{^authMethods}}Endpoints do not require authorization.{{/authMethods}}
{{#hasAuthMethods}}Authentication schemes defined for the API:{{/hasAuthMethods}}
{{#authMethods}}
<a id="{{name}}"></a>
### {{name}}

{{#isApiKey}}

- **Type**: API key
- **API key parameter name**: {{keyParamName}}
- **Location**: {{#isKeyInQuery}}URL query string{{/isKeyInQuery}}{{#isKeyInHeader}}HTTP header{{/isKeyInHeader}}
{{/isApiKey}}
{{#isBasicBasic}}

- **Type**: HTTP basic authentication
{{/isBasicBasic}}
{{#isBasicBearer}}

- **Type**: HTTP Bearer Token authentication{{#bearerFormat}} ({{{.}}}){{/bearerFormat}}
{{/isBasicBearer}}
{{#isHttpSignature}}

- **Type**: HTTP signature authentication
{{/isHttpSignature}}
{{#isOAuth}}

- **Type**: OAuth
- **Flow**: {{flow}}
- **Authorization URL**: {{authorizationUrl}}
- **Scopes**: {{^scopes}}N/A{{/scopes}}
{{#scopes}}  - {{scope}}: {{description}}
{{/scopes}}
{{/isOAuth}}

{{/authMethods}}

## Recommendation

It's recommended to create an instance of `ApiClient` per thread in a multithreaded environment to avoid any potential issues.
However, the instances of the api clients created from the `ApiClient` are thread-safe and can be re-used.

## Author

{{#apiInfo}}{{#apis}}{{#-last}}{{infoEmail}}
{{/-last}}{{/apis}}{{/apiInfo}}
