{{>licenseInfo}}

package {{package}};

import {{invokerPackage}}.ApiException;
{{#imports}}import {{import}};
{{/imports}}
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;

import java.time.LocalDate;
import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

{{#useBeanValidation}}
import {{javaxPackage}}.validation.constraints.*;
import {{javaxPackage}}.validation.Valid;

{{/useBeanValidation}}
/**
 * API tests for {{classname}}
 */
@Disabled
public class {{classname}}Test {

    private final {{classname}} api = new {{classname}}();

    {{#operations}}
    {{#operation}}
    /**
    {{#summary}}
     * {{summary}}
     *
    {{/summary}}
    {{#notes}}
     * {{notes}}
     *
    {{/notes}}
     * @throws ApiException
     *          if the Api call fails
     */
    @Test
    public void {{operationId}}Test() throws ApiException {
        {{#allParams}}
        {{{dataType}}} {{paramName}} = null;
        {{/allParams}}
        {{#returnType}}{{{returnType}}} response = {{/returnType}}api.{{operationId}}({{#allParams}}{{paramName}}{{^-last}}, {{/-last}}{{/allParams}});

        // TODO: test validations
    }
    {{/operation}}
    {{/operations}}
}
