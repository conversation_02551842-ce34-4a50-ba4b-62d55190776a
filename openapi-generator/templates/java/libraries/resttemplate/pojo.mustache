/**
 * {{description}}{{^description}}{{classname}}{{/description}}{{#isDeprecated}}
 * @deprecated{{/isDeprecated}}
 */{{#isDeprecated}}
@Deprecated{{/isDeprecated}}
{{#swagger1AnnotationLibrary}}
{{#description}}
@ApiModel(description = "{{{.}}}")
{{/description}}
{{/swagger1AnnotationLibrary}}
{{#swagger2AnnotationLibrary}}
{{#description}}
@Schema(description = "{{{.}}}")
{{/description}}
{{/swagger2AnnotationLibrary}}
{{#jackson}}
@JsonPropertyOrder({
{{#vars}}
  {{classname}}.JSON_PROPERTY_{{nameInSnakeCase}}{{^-last}},{{/-last}}
{{/vars}}
})
{{#isClassnameSanitized}}
{{^hasDiscriminatorWithNonEmptyMapping}}
@JsonTypeName("{{name}}")
{{/hasDiscriminatorWithNonEmptyMapping}}
{{/isClassnameSanitized}}
{{/jackson}}
{{>additionalModelTypeAnnotations}}{{#discriminator}}{{>typeInfoAnnotation}}{{/discriminator}}{{>xmlAnnotation}}
{{#vendorExtensions.x-class-extra-annotation}}
{{{vendorExtensions.x-class-extra-annotation}}}
{{/vendorExtensions.x-class-extra-annotation}}
public class {{classname}} {{#parent}}extends {{{.}}} {{/parent}}{{#vendorExtensions.x-implements}}{{#-first}}implements {{{.}}}{{/-first}}{{^-first}}, {{{.}}}{{/-first}}{{#-last}} {{/-last}}{{/vendorExtensions.x-implements}}{
{{#serializableModel}}
  private static final long serialVersionUID = 1L;

{{/serializableModel}}
  {{#vars}}
    {{#isEnum}}
    {{^isContainer}}
{{>modelInnerEnum}}
    {{/isContainer}}
    {{#isContainer}}
    {{#mostInnerItems}}
{{>modelInnerEnum}}
    {{/mostInnerItems}}
    {{/isContainer}}
    {{/isEnum}}
  {{#gson}}
  public static final String SERIALIZED_NAME_{{nameInSnakeCase}} = "{{baseName}}";
  {{/gson}}
  {{#jackson}}
  public static final String JSON_PROPERTY_{{nameInSnakeCase}} = "{{baseName}}";
  {{/jackson}}
  {{#withXml}}
  @Xml{{#isXmlAttribute}}Attribute{{/isXmlAttribute}}{{^isXmlAttribute}}Element{{/isXmlAttribute}}(name = "{{items.xmlName}}{{^items.xmlName}}{{xmlName}}{{^xmlName}}{{baseName}}{{/xmlName}}{{/items.xmlName}}"{{#xmlNamespace}}, namespace = "{{.}}"{{/xmlNamespace}})
    {{#isXmlWrapped}}
  @XmlElementWrapper(name = "{{xmlName}}{{^xmlName}}{{baseName}}{{/xmlName}}"{{#xmlNamespace}}, namespace = "{{.}}"{{/xmlNamespace}})
    {{/isXmlWrapped}}
    {{^isXmlAttribute}}
      {{#isDateTime}}
  @XmlJavaTypeAdapter(OffsetDateTimeXmlAdapter.class)
      {{/isDateTime}}
    {{/isXmlAttribute}}
  {{/withXml}}
  {{#gson}}
  @SerializedName(SERIALIZED_NAME_{{nameInSnakeCase}})
  {{/gson}}
  {{>nullable_var_annotations}}
  {{#vendorExtensions.x-field-extra-annotation}}
  {{{vendorExtensions.x-field-extra-annotation}}}
  {{/vendorExtensions.x-field-extra-annotation}}
  {{#vendorExtensions.x-is-jackson-optional-nullable}}
  {{#isContainer}}
  {{#hasChildren}}protected{{/hasChildren}}{{^hasChildren}}private{{/hasChildren}} JsonNullable<{{{datatypeWithEnum}}}> {{name}} = JsonNullable.<{{{datatypeWithEnum}}}>undefined();
  {{/isContainer}}
  {{^isContainer}}
  {{#hasChildren}}protected{{/hasChildren}}{{^hasChildren}}private{{/hasChildren}} JsonNullable<{{{datatypeWithEnum}}}> {{name}} = JsonNullable.<{{{datatypeWithEnum}}}>{{#defaultValue}}of({{{.}}}){{/defaultValue}}{{^defaultValue}}undefined(){{/defaultValue}};
  {{/isContainer}}
  {{/vendorExtensions.x-is-jackson-optional-nullable}}
  {{^vendorExtensions.x-is-jackson-optional-nullable}}
  {{#isContainer}}
  {{#hasChildren}}protected{{/hasChildren}}{{^hasChildren}}private{{/hasChildren}} {{{datatypeWithEnum}}} {{name}}{{#defaultValue}} = {{{.}}}{{/defaultValue}};
  {{/isContainer}}
  {{^isContainer}}
  {{#hasChildren}}protected{{/hasChildren}}{{^hasChildren}}private{{/hasChildren}} {{{datatypeWithEnum}}} {{name}}{{#defaultValue}} = {{{.}}}{{/defaultValue}};
  {{/isContainer}}
  {{/vendorExtensions.x-is-jackson-optional-nullable}}

  {{/vars}}
  public {{classname}}() {
    {{#parent}}
    {{#parcelableModel}}
    super();{{/parcelableModel}}
    {{/parent}}
    {{#gson}}
    {{#discriminator}}
    {{#discriminator.isEnum}}
    this.{{{discriminatorName}}} = this.getClass().getSimpleName();
    {{/discriminator.isEnum}}
    {{/discriminator}}
    {{/gson}}
  }
  {{#vendorExtensions.x-has-readonly-properties}}
  {{^withXml}}
  /**
   * Constructor with only readonly parameters{{#generateConstructorWithAllArgs}}{{^vendorExtensions.x-java-all-args-constructor}} and all parameters{{/vendorExtensions.x-java-all-args-constructor}}{{/generateConstructorWithAllArgs}}
   */
  {{#jsonb}}@JsonbCreator{{/jsonb}}{{#jackson}}@JsonCreator{{/jackson}}
  public {{classname}}(
  {{#readOnlyVars}}
    {{#jsonb}}@JsonbProperty(value = "{{baseName}}"{{^required}}, nullable = true{{/required}}){{/jsonb}}{{#jackson}}@JsonProperty(JSON_PROPERTY_{{nameInSnakeCase}}){{/jackson}} {{{datatypeWithEnum}}} {{name}}{{^-last}}, {{/-last}}
  {{/readOnlyVars}}
  ) {
    this();
  {{#readOnlyVars}}
    this.{{name}} = {{#vendorExtensions.x-is-jackson-optional-nullable}}{{name}} == null ? JsonNullable.<{{{datatypeWithEnum}}}>undefined() : JsonNullable.of({{name}}){{/vendorExtensions.x-is-jackson-optional-nullable}}{{^vendorExtensions.x-is-jackson-optional-nullable}}{{name}}{{/vendorExtensions.x-is-jackson-optional-nullable}};
  {{/readOnlyVars}}
  }
  {{/withXml}}
  {{/vendorExtensions.x-has-readonly-properties}}
{{#vendorExtensions.x-java-all-args-constructor}}

  /**
   * Constructor with all args parameters
   */
  public {{classname}}({{#vendorExtensions.x-java-all-args-constructor-vars}}{{#jsonb}}@JsonbProperty(value = "{{baseName}}"{{^required}}, nullable = true{{/required}}){{/jsonb}}{{#jackson}}@JsonProperty(JSON_PROPERTY_{{nameInSnakeCase}}){{/jackson}} {{{datatypeWithEnum}}} {{name}}{{^-last}}, {{/-last}}{{/vendorExtensions.x-java-all-args-constructor-vars}}) {
{{#parent}}
    super({{#parentVars}}{{name}}{{^-last}}, {{/-last}}{{/parentVars}});
{{/parent}}
  {{#vars}}
    this.{{name}} = {{#vendorExtensions.x-is-jackson-optional-nullable}}{{name}} == null ? JsonNullable.<{{{datatypeWithEnum}}}>undefined() : JsonNullable.of({{name}}){{/vendorExtensions.x-is-jackson-optional-nullable}}{{^vendorExtensions.x-is-jackson-optional-nullable}}{{name}}{{/vendorExtensions.x-is-jackson-optional-nullable}};
{{/vars}}
  }
{{/vendorExtensions.x-java-all-args-constructor}}

{{#vars}}
  {{^isReadOnly}}
  public {{classname}} {{name}}({{>nullable_var_annotations}} {{{datatypeWithEnum}}} {{name}}) {
    {{#vendorExtensions.x-is-jackson-optional-nullable}}this.{{name}} = JsonNullable.<{{{datatypeWithEnum}}}>of({{name}});{{/vendorExtensions.x-is-jackson-optional-nullable}}
    {{^vendorExtensions.x-is-jackson-optional-nullable}}this.{{name}} = {{name}};{{/vendorExtensions.x-is-jackson-optional-nullable}}
    return this;
  }
  {{#isArray}}

  public {{classname}} add{{nameInPascalCase}}Item({{{items.datatypeWithEnum}}} {{name}}Item) {
    {{#vendorExtensions.x-is-jackson-optional-nullable}}
    if (this.{{name}} == null || !this.{{name}}.isPresent()) {
      this.{{name}} = JsonNullable.<{{{datatypeWithEnum}}}>of({{{defaultValue}}}{{^defaultValue}}new {{#uniqueItems}}LinkedHashSet{{/uniqueItems}}{{^uniqueItems}}ArrayList{{/uniqueItems}}<>(){{/defaultValue}});
    }
    try {
      this.{{name}}.get().add({{name}}Item);
    } catch (java.util.NoSuchElementException e) {
      // this can never happen, as we make sure above that the value is present
    }
    return this;
    {{/vendorExtensions.x-is-jackson-optional-nullable}}
    {{^vendorExtensions.x-is-jackson-optional-nullable}}
    if (this.{{name}} == null) {
      this.{{name}} = {{{defaultValue}}}{{^defaultValue}}new {{#uniqueItems}}LinkedHashSet{{/uniqueItems}}{{^uniqueItems}}ArrayList{{/uniqueItems}}<>(){{/defaultValue}};
    }
    this.{{name}}.add({{name}}Item);
    return this;
    {{/vendorExtensions.x-is-jackson-optional-nullable}}
  }
  {{/isArray}}
  {{#isMap}}

  public {{classname}} put{{nameInPascalCase}}Item(String key, {{{items.datatypeWithEnum}}} {{name}}Item) {
    {{#vendorExtensions.x-is-jackson-optional-nullable}}
    if (this.{{name}} == null || !this.{{name}}.isPresent()) {
      this.{{name}} = JsonNullable.<{{{datatypeWithEnum}}}>of({{{defaultValue}}}{{^defaultValue}}new HashMap<>(){{/defaultValue}});
    }
    try {
      this.{{name}}.get().put(key, {{name}}Item);
    } catch (java.util.NoSuchElementException e) {
      // this can never happen, as we make sure above that the value is present
    }
    return this;
    {{/vendorExtensions.x-is-jackson-optional-nullable}}
    {{^vendorExtensions.x-is-jackson-optional-nullable}}
    {{^required}}
    if (this.{{name}} == null) {
      this.{{name}} = {{{defaultValue}}}{{^defaultValue}}new HashMap<>(){{/defaultValue}};
    }
    {{/required}}
    this.{{name}}.put(key, {{name}}Item);
    return this;
    {{/vendorExtensions.x-is-jackson-optional-nullable}}
  }
  {{/isMap}}

  {{/isReadOnly}}
  /**
  {{#description}}
   * {{.}}
  {{/description}}
  {{^description}}
   * Get {{name}}
  {{/description}}
  {{#minimum}}
   * minimum: {{.}}
  {{/minimum}}
  {{#maximum}}
   * maximum: {{.}}
  {{/maximum}}
   * @return {{name}}
   {{#deprecated}}
   * @deprecated
   {{/deprecated}}
   */
{{#deprecated}}
  @Deprecated
{{/deprecated}}
  {{>nullable_var_annotations}}
{{#jsonb}}
  @JsonbProperty("{{baseName}}")
{{/jsonb}}
{{#useBeanValidation}}
{{>beanValidation}}
{{/useBeanValidation}}
{{#swagger1AnnotationLibrary}}
  @ApiModelProperty({{#example}}example = "{{{.}}}", {{/example}}{{#required}}required = {{required}}, {{/required}}value = "{{{description}}}")
{{/swagger1AnnotationLibrary}}
{{#swagger2AnnotationLibrary}}
  @Schema({{#example}}example = "{{{.}}}", {{/example}}requiredMode = {{#required}}Schema.RequiredMode.REQUIRED{{/required}}{{^required}}Schema.RequiredMode.NOT_REQUIRED{{/required}}, description = "{{{description}}}")
{{/swagger2AnnotationLibrary}}
{{#vendorExtensions.x-extra-annotation}}
  {{{vendorExtensions.x-extra-annotation}}}
{{/vendorExtensions.x-extra-annotation}}
{{#vendorExtensions.x-is-jackson-optional-nullable}}
  {{!unannotated, Jackson would pick this up automatically and add it *in addition* to the _JsonNullable getter field}}
  @JsonIgnore
{{/vendorExtensions.x-is-jackson-optional-nullable}}
{{^vendorExtensions.x-is-jackson-optional-nullable}}{{#jackson}}{{> jackson_annotations}}{{/jackson}}{{/vendorExtensions.x-is-jackson-optional-nullable}}
  public {{{datatypeWithEnum}}} {{getter}}() {
    {{#vendorExtensions.x-is-jackson-optional-nullable}}
    {{#isReadOnly}}{{! A readonly attribute doesn't have setter => jackson will set null directly if explicitly returned by API, so make sure we have an empty JsonNullable}}
    if ({{name}} == null) {
      {{name}} = JsonNullable.<{{{datatypeWithEnum}}}>{{#defaultValue}}of({{{.}}}){{/defaultValue}}{{^defaultValue}}undefined(){{/defaultValue}};
    }
    {{/isReadOnly}}
    return {{name}}.orElse(null);
    {{/vendorExtensions.x-is-jackson-optional-nullable}}
    {{^vendorExtensions.x-is-jackson-optional-nullable}}
    return {{name}};
    {{/vendorExtensions.x-is-jackson-optional-nullable}}
  }

  {{#vendorExtensions.x-is-jackson-optional-nullable}}
{{> jackson_annotations}}
  public JsonNullable<{{{datatypeWithEnum}}}> {{getter}}_JsonNullable() {
    return {{name}};
  }
  {{/vendorExtensions.x-is-jackson-optional-nullable}}{{#vendorExtensions.x-is-jackson-optional-nullable}}
  @JsonProperty(JSON_PROPERTY_{{nameInSnakeCase}})
  {{#isReadOnly}}private{{/isReadOnly}}{{^isReadOnly}}public{{/isReadOnly}} void {{setter}}_JsonNullable(JsonNullable<{{{datatypeWithEnum}}}> {{name}}) {
    {{! For getters/setters that have name differing from attribute name, we must include setter (albeit private) for jackson to be able to set the attribute}}
    this.{{name}} = {{name}};
  }
  {{/vendorExtensions.x-is-jackson-optional-nullable}}

  {{^isReadOnly}}
{{#vendorExtensions.x-setter-extra-annotation}}  {{{vendorExtensions.x-setter-extra-annotation}}}
{{/vendorExtensions.x-setter-extra-annotation}}{{#jackson}}{{^vendorExtensions.x-is-jackson-optional-nullable}}{{> jackson_annotations}}{{/vendorExtensions.x-is-jackson-optional-nullable}}{{/jackson}}  public void {{setter}}({{>nullable_var_annotations}} {{{datatypeWithEnum}}} {{name}}) {
    {{#vendorExtensions.x-is-jackson-optional-nullable}}
    this.{{name}} = JsonNullable.<{{{datatypeWithEnum}}}>of({{name}});
    {{/vendorExtensions.x-is-jackson-optional-nullable}}
    {{^vendorExtensions.x-is-jackson-optional-nullable}}
    this.{{name}} = {{name}};
    {{/vendorExtensions.x-is-jackson-optional-nullable}}
  }
  {{/isReadOnly}}

  {{/vars}}
{{>libraries/resttemplate/additional_properties}}
  {{#parent}}
  {{#readWriteVars}}
  {{#isOverridden}}
  @Override
  public {{classname}} {{name}}({{>nullable_var_annotations}} {{{datatypeWithEnum}}} {{name}}) {
    {{#vendorExtensions.x-is-jackson-optional-nullable}}
    this.{{setter}}(JsonNullable.<{{{datatypeWithEnum}}}>of({{name}}));
    {{/vendorExtensions.x-is-jackson-optional-nullable}}
    {{^vendorExtensions.x-is-jackson-optional-nullable}}
    this.{{setter}}({{name}});
    {{/vendorExtensions.x-is-jackson-optional-nullable}}
    return this;
  }

  {{/isOverridden}}
  {{/readWriteVars}}
  {{/parent}}
  @Override
  public boolean equals(Object o) {
  {{#useReflectionEqualsHashCode}}
    return EqualsBuilder.reflectionEquals(this, o, false, null, true);
  {{/useReflectionEqualsHashCode}}
  {{^useReflectionEqualsHashCode}}
    if (this == o) {
      return true;
    }
    if (o == null || getClass() != o.getClass()) {
      return false;
    }{{#hasVars}}
    {{classname}} {{classVarName}} = ({{classname}}) o;
    return {{#vars}}{{#vendorExtensions.x-is-jackson-optional-nullable}}equalsNullable(this.{{name}}, {{classVarName}}.{{name}}){{/vendorExtensions.x-is-jackson-optional-nullable}}{{^vendorExtensions.x-is-jackson-optional-nullable}}{{#isByteArray}}Arrays{{/isByteArray}}{{^isByteArray}}Objects{{/isByteArray}}.equals(this.{{name}}, {{classVarName}}.{{name}}){{/vendorExtensions.x-is-jackson-optional-nullable}}{{^-last}} &&
        {{/-last}}{{/vars}}{{#additionalPropertiesType}} &&
        Objects.equals(this.additionalProperties, {{classVarName}}.additionalProperties){{/additionalPropertiesType}}{{#parent}} &&
        super.equals(o){{/parent}};{{/hasVars}}{{^hasVars}}
    return {{#parent}}super.equals(o){{/parent}}{{^parent}}true{{/parent}};{{/hasVars}}
  {{/useReflectionEqualsHashCode}}
  }{{#vendorExtensions.x-jackson-optional-nullable-helpers}}

  private static <T> boolean equalsNullable(JsonNullable<T> a, JsonNullable<T> b) {
    return a == b || (a != null && b != null && a.isPresent() && b.isPresent() && Objects.deepEquals(a.get(), b.get()));
  }{{/vendorExtensions.x-jackson-optional-nullable-helpers}}

  @Override
  public int hashCode() {
  {{#useReflectionEqualsHashCode}}
    return HashCodeBuilder.reflectionHashCode(this);
  {{/useReflectionEqualsHashCode}}
  {{^useReflectionEqualsHashCode}}
    return Objects.hash({{#vars}}{{#vendorExtensions.x-is-jackson-optional-nullable}}hashCodeNullable({{name}}){{/vendorExtensions.x-is-jackson-optional-nullable}}{{^vendorExtensions.x-is-jackson-optional-nullable}}{{^isByteArray}}{{name}}{{/isByteArray}}{{#isByteArray}}Arrays.hashCode({{name}}){{/isByteArray}}{{/vendorExtensions.x-is-jackson-optional-nullable}}{{^-last}}, {{/-last}}{{/vars}}{{#parent}}{{#hasVars}}, {{/hasVars}}super.hashCode(){{/parent}}{{#additionalPropertiesType}}, additionalProperties{{/additionalPropertiesType}});
  {{/useReflectionEqualsHashCode}}
  }{{#vendorExtensions.x-jackson-optional-nullable-helpers}}

  private static <T> int hashCodeNullable(JsonNullable<T> a) {
    if (a == null) {
      return 1;
    }
    return a.isPresent() ? Arrays.deepHashCode(new Object[]{a.get()}) : 31;
  }{{/vendorExtensions.x-jackson-optional-nullable-helpers}}

  @Override
  public String toString() {
    StringBuilder sb = new StringBuilder();
    sb.append("class {{classname}} {\n");
    {{#parent}}
    sb.append("    ").append(toIndentedString(super.toString())).append("\n");
    {{/parent}}
    {{#vars}}
    sb.append("    {{name}}: ").append({{#isPassword}}"*"{{/isPassword}}{{^isPassword}}toIndentedString({{name}}){{/isPassword}}).append("\n");
    {{/vars}}
    {{#additionalPropertiesType}}
    sb.append("    additionalProperties: ").append(toIndentedString(additionalProperties)).append("\n");
    {{/additionalPropertiesType}}
    sb.append("}");
    return sb.toString();
  }

  /**
   * Convert the given object to string with each line indented by 4 spaces
   * (except the first line).
   */
  private{{#jsonb}} static{{/jsonb}} String toIndentedString(Object o) {
    if (o == null) {
      return "null";
    }
    return o.toString().replace("\n", "\n    ");
  }
{{#supportUrlQuery}}

  /**
   * Convert the instance into URL query string.
   *
   * @return URL query string
   */
  public String toUrlQueryString() {
    return toUrlQueryString(null);
  }

  /**
   * Convert the instance into URL query string.
   *
   * @param prefix prefix of the query string
   * @return URL query string
   */
  public String toUrlQueryString(String prefix) {
    String suffix = "";
    String containerSuffix = "";
    String containerPrefix = "";
    if (prefix == null) {
      // style=form, explode=true, e.g. /pet?name=cat&type=manx
      prefix = "";
    } else {
      // deepObject style e.g. /pet?id[name]=cat&id[type]=manx
      prefix = prefix + "[";
      suffix = "]";
      containerSuffix = "]";
      containerPrefix = "[";
    }

    StringJoiner joiner = new StringJoiner("&");

    {{#allVars}}
    // add `{{baseName}}` to the URL query string
    {{#isArray}}
    {{#items.isPrimitiveType}}
    {{#uniqueItems}}
    if ({{getter}}() != null) {
      int i = 0;
      for ({{{items.datatypeWithEnum}}} _item : {{getter}}()) {
        try {
          joiner.add(String.format("%s{{baseName}}%s%s=%s", prefix, suffix,
              "".equals(suffix) ? "" : String.format("%s%d%s", containerPrefix, i, containerSuffix),
              URLEncoder.encode(String.valueOf(_item), "UTF-8").replaceAll("\\+", "%20")));
        } catch (UnsupportedEncodingException e) {
          // Should never happen, UTF-8 is always supported
          throw new RuntimeException(e);
        }
      }
      i++;
    }
    {{/uniqueItems}}
    {{^uniqueItems}}
    if ({{getter}}() != null) {
      for (int i = 0; i < {{getter}}().size(); i++) {
        try {
          joiner.add(String.format("%s{{baseName}}%s%s=%s", prefix, suffix,
              "".equals(suffix) ? "" : String.format("%s%d%s", containerPrefix, i, containerSuffix),
              URLEncoder.encode(String.valueOf({{getter}}().get(i)), "UTF-8").replaceAll("\\+", "%20")));
        } catch (UnsupportedEncodingException e) {
          // Should never happen, UTF-8 is always supported
          throw new RuntimeException(e);
        }
      }
    }
    {{/uniqueItems}}
    {{/items.isPrimitiveType}}
    {{^items.isPrimitiveType}}
    {{#items.isModel}}
    {{#uniqueItems}}
    if ({{getter}}() != null) {
      int i = 0;
      for ({{{items.dataType}}} _item : {{getter}}()) {
        if (_item != null) {
          joiner.add(_item.toUrlQueryString(String.format("%s{{baseName}}%s%s", prefix, suffix,
              "".equals(suffix) ? "" : String.format("%s%d%s", containerPrefix, i, containerSuffix))));
        }
      }
      i++;
    }
    {{/uniqueItems}}
    {{^uniqueItems}}
    if ({{getter}}() != null) {
      for (int i = 0; i < {{getter}}().size(); i++) {
        if ({{getter}}().get(i) != null) {
          joiner.add({{getter}}().get(i).toUrlQueryString(String.format("%s{{baseName}}%s%s", prefix, suffix,
              "".equals(suffix) ? "" : String.format("%s%d%s", containerPrefix, i, containerSuffix))));
        }
      }
    }
    {{/uniqueItems}}
    {{/items.isModel}}
    {{^items.isModel}}
    {{#uniqueItems}}
    if ({{getter}}() != null) {
      int i = 0;
      for ({{{items.dataType}}} _item : {{getter}}()) {
        if (_item != null) {
          try {
            joiner.add(String.format("%s{{baseName}}%s%s=%s", prefix, suffix,
                "".equals(suffix) ? "" : String.format("%s%d%s", containerPrefix, i, containerSuffix),
                URLEncoder.encode(String.valueOf(_item), "UTF-8").replaceAll("\\+", "%20")));
          } catch (UnsupportedEncodingException e) {
            // Should never happen, UTF-8 is always supported
            throw new RuntimeException(e);
          }
        }
        i++;
      }
    }
    {{/uniqueItems}}
    {{^uniqueItems}}
    if ({{getter}}() != null) {
      for (int i = 0; i < {{getter}}().size(); i++) {
        if ({{getter}}().get(i) != null) {
          try {
            joiner.add(String.format("%s{{baseName}}%s%s=%s", prefix, suffix,
                "".equals(suffix) ? "" : String.format("%s%d%s", containerPrefix, i, containerSuffix),
                URLEncoder.encode(String.valueOf({{getter}}().get(i)), "UTF-8").replaceAll("\\+", "%20")));
          } catch (UnsupportedEncodingException e) {
            // Should never happen, UTF-8 is always supported
            throw new RuntimeException(e);
          }
        }
      }
    }
    {{/uniqueItems}}
    {{/items.isModel}}
    {{/items.isPrimitiveType}}
    {{/isArray}}
    {{^isArray}}
    {{#isMap}}
    {{^items.isModel}}
    if ({{getter}}() != null) {
      for (String _key : {{getter}}().keySet()) {
        try {
          joiner.add(String.format("%s{{baseName}}%s%s=%s", prefix, suffix,
              "".equals(suffix) ? "" : String.format("%s%d%s", containerPrefix, _key, containerSuffix),
              {{getter}}().get(_key), URLEncoder.encode(String.valueOf({{getter}}().get(_key)), "UTF-8").replaceAll("\\+", "%20")));
        } catch (UnsupportedEncodingException e) {
          // Should never happen, UTF-8 is always supported
          throw new RuntimeException(e);
        }
      }
    }
    {{/items.isModel}}
    {{#items.isModel}}
    if ({{getter}}() != null) {
      for (String _key : {{getter}}().keySet()) {
        if ({{getter}}().get(_key) != null) {
          joiner.add({{getter}}().get(_key).toUrlQueryString(String.format("%s{{baseName}}%s%s", prefix, suffix,
              "".equals(suffix) ? "" : String.format("%s%d%s", containerPrefix, _key, containerSuffix))));
        }
      }
    }
    {{/items.isModel}}
    {{/isMap}}
    {{^isMap}}
    {{#isPrimitiveType}}
    if ({{getter}}() != null) {
      try {
        joiner.add(String.format("%s{{{baseName}}}%s=%s", prefix, suffix, URLEncoder.encode(String.valueOf({{{getter}}}()), "UTF-8").replaceAll("\\+", "%20")));
      } catch (UnsupportedEncodingException e) {
        // Should never happen, UTF-8 is always supported
        throw new RuntimeException(e);
      }
    }
    {{/isPrimitiveType}}
    {{^isPrimitiveType}}
    {{#isModel}}
    if ({{getter}}() != null) {
      joiner.add({{getter}}().toUrlQueryString(prefix + "{{{baseName}}}" + suffix));
    }
    {{/isModel}}
    {{^isModel}}
    if ({{getter}}() != null) {
      try {
        joiner.add(String.format("%s{{{baseName}}}%s=%s", prefix, suffix, URLEncoder.encode(String.valueOf({{{getter}}}()), "UTF-8").replaceAll("\\+", "%20")));
      } catch (UnsupportedEncodingException e) {
        // Should never happen, UTF-8 is always supported
        throw new RuntimeException(e);
      }
    }
    {{/isModel}}
    {{/isPrimitiveType}}
    {{/isMap}}
    {{/isArray}}

    {{/allVars}}
    return joiner.toString();
  }
{{/supportUrlQuery}}
{{#parcelableModel}}

  public void writeToParcel(Parcel out, int flags) {
{{#model}}
{{#isArray}}
    out.writeList(this);
{{/isArray}}
{{^isArray}}
{{#parent}}
    super.writeToParcel(out, flags);
{{/parent}}
{{#vars}}
    out.writeValue({{name}});
{{/vars}}
{{/isArray}}
{{/model}}
  }

  {{classname}}(Parcel in) {
{{#isArray}}
    in.readTypedList(this, {{arrayModelType}}.CREATOR);
{{/isArray}}
{{^isArray}}
{{#parent}}
    super(in);
{{/parent}}
{{#vars}}
{{#isPrimitiveType}}
    {{name}} = ({{{datatypeWithEnum}}})in.readValue(null);
{{/isPrimitiveType}}
{{^isPrimitiveType}}
    {{name}} = ({{{datatypeWithEnum}}})in.readValue({{complexType}}.class.getClassLoader());
{{/isPrimitiveType}}
{{/vars}}
{{/isArray}}
  }

  public int describeContents() {
    return 0;
  }

  public static final Parcelable.Creator<{{classname}}> CREATOR = new Parcelable.Creator<{{classname}}>() {
    public {{classname}} createFromParcel(Parcel in) {
{{#model}}
{{#isArray}}
      {{classname}} result = new {{classname}}();
      result.addAll(in.readArrayList({{arrayModelType}}.class.getClassLoader()));
      return result;
{{/isArray}}
{{^isArray}}
      return new {{classname}}(in);
{{/isArray}}
{{/model}}
    }
    public {{classname}}[] newArray(int size) {
      return new {{classname}}[size];
    }
  };
{{/parcelableModel}}
{{#generateBuilders}}

  {{>javaBuilder}}
{{/generateBuilders}}

}
