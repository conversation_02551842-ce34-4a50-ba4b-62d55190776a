apply plugin: 'idea'
apply plugin: 'eclipse'

group = '{{groupId}}'
version = '{{artifactVersion}}'

buildscript {
    repositories {
        mavenCentral()
    }
    dependencies {
        classpath 'com.android.tools.build:gradle:1.5.+'
        classpath 'com.github.dcendents:android-maven-gradle-plugin:1.3'
    }
}

repositories {
    mavenCentral()
}


if(hasProperty('target') && target == 'android') {

    apply plugin: 'com.android.library'
    apply plugin: 'com.github.dcendents.android-maven'

    android {
        compileSdkVersion 23
        buildToolsVersion '23.0.2'
        defaultConfig {
            minSdkVersion 14
            targetSdkVersion 22
        }
        compileOptions {
            {{#useJakartaEe}}
            sourceCompatibility JavaVersion.VERSION_17
            targetCompatibility JavaVersion.VERSION_17
            {{/useJakartaEe}}
            {{^useJakartaEe}}
            sourceCompatibility JavaVersion.VERSION_1_8
            targetCompatibility JavaVersion.VERSION_1_8
            {{/useJakartaEe}}
        }

        // Rename the aar correctly
        libraryVariants.all { variant ->
            variant.outputs.each { output ->
                def outputFile = output.outputFile
                if (outputFile != null && outputFile.name.endsWith('.aar')) {
                    def fileName = "${project.name}-${variant.baseName}-${version}.aar"
                    output.outputFile = new File(outputFile.parent, fileName)
                }
            }
        }

        dependencies {
            provided "jakarta.annotation:jakarta.annotation-api:$jakarta_annotation_version"
        }
    }

    afterEvaluate {
        android.libraryVariants.all { variant ->
            def task = project.tasks.create "jar${variant.name.capitalize()}", Jar
            task.description = "Create jar artifact for ${variant.name}"
            task.dependsOn variant.javaCompile
            task.from variant.javaCompile.destinationDirectory
            task.destinationDirectory = project.file("${project.buildDir}/outputs/jar")
            task.archiveFileName = "${project.name}-${variant.baseName}-${version}.jar"
            artifacts.add('archives', task);
        }
    }

    task sourcesJar(type: Jar) {
        from android.sourceSets.main.java.srcDirs
        archiveClassifier = 'sources'
    }

    artifacts {
        archives sourcesJar
    }

} else {

    apply plugin: 'java'
    apply plugin: 'maven-publish'

    {{#useJakartaEe}}
    sourceCompatibility = JavaVersion.VERSION_17
    targetCompatibility = JavaVersion.VERSION_17
    {{/useJakartaEe}}
    {{^useJakartaEe}}
    sourceCompatibility = JavaVersion.VERSION_1_8
    targetCompatibility = JavaVersion.VERSION_1_8
    {{/useJakartaEe}}

    publishing {
        publications {
            maven(MavenPublication) {
               artifactId = '{{artifactId}}'
               from components.java
            }
        }
    }

    task execute(type:JavaExec) {
       mainClass = System.getProperty('mainClass')
       classpath = sourceSets.main.runtimeClasspath
    }
}

ext {
    {{#swagger1AnnotationLibrary}}
    swagger_annotations_version = "1.6.9"
    {{/swagger1AnnotationLibrary}}
    {{#swagger2AnnotationLibrary}}
    swagger_annotations_version = "2.2.9"
    {{/swagger2AnnotationLibrary}}
    jackson_version = "2.17.1"
    jackson_databind_version = "2.17.1"
    {{#openApiNullable}}
    jackson_databind_nullable_version = "0.2.6"
    {{/openApiNullable}}
    {{#useJakartaEe}}
    spring_web_version = "6.1.5"
    jakarta_annotation_version = "2.1.1"
    beanvalidation_version = "3.0.2"
    {{/useJakartaEe}}
    {{^useJakartaEe}}
    spring_web_version = "6.1.13"
    jakarta_annotation_version = "1.3.5"
    beanvalidation_version = "2.0.2"
    {{/useJakartaEe}}
    jodatime_version = "2.9.9"
    junit_version = "5.10.2"
}

dependencies {
    {{#swagger1AnnotationLibrary}}
    implementation "io.swagger:swagger-annotations:$swagger_annotations_version"
    {{/swagger1AnnotationLibrary}}
    {{#swagger2AnnotationLibrary}}
    implementation "io.swagger.core.v3:swagger-annotations:$swagger_annotations_version"
    {{/swagger2AnnotationLibrary}}
    implementation "com.google.code.findbugs:jsr305:3.0.2"
    implementation "org.springframework:spring-web:$spring_web_version"
    implementation "org.springframework:spring-context:$spring_web_version"
    implementation "com.fasterxml.jackson.core:jackson-core:$jackson_version"
    implementation "com.fasterxml.jackson.core:jackson-annotations:$jackson_version"
    implementation "com.fasterxml.jackson.core:jackson-databind:$jackson_databind_version"
    {{^useJakartaEe}}
    implementation "com.fasterxml.jackson.jaxrs:jackson-jaxrs-json-provider:$jackson_version"
    {{/useJakartaEe}}
    {{#useJakartaEe}}
    implementation "com.fasterxml.jackson.jakarta.rs:jackson-jakarta-rs-json-provider:$jackson_version"
    {{/useJakartaEe}}
    {{#openApiNullable}}
    implementation "org.openapitools:jackson-databind-nullable:$jackson_databind_nullable_version"
    {{/openApiNullable}}
    implementation "com.fasterxml.jackson.datatype:jackson-datatype-jsr310:$jackson_version"
    {{#joda}}
    implementation "com.fasterxml.jackson.datatype:jackson-datatype-joda:$jackson_version"
    implementation "joda-time:joda-time:$jodatime_version"
    {{/joda}}
    {{#withXml}}
    implementation "com.fasterxml.jackson.dataformat:jackson-dataformat-xml:$jackson_version"
    {{/withXml}}
    implementation "jakarta.annotation:jakarta.annotation-api:$jakarta_annotation_version"
    testImplementation "org.junit.jupiter:junit-jupiter-api:$junit_version"
    testRuntimeOnly "org.junit.jupiter:junit-jupiter-engine:$junit_version"
}

test {
    // Enable JUnit 5 (Gradle 4.6+).
    useJUnitPlatform()

    // Always run tests, even when nothing changed.
    dependsOn 'cleanTest'

    // Show test results.
    testLogging {
        events "passed", "skipped", "failed"
    }

}
