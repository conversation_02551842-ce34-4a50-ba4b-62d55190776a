# {{appName}} - MicroProfile Rest Client & MicroProfile Server

{{#appDescriptionWithNewLines}}
{{{.}}}

{{/appDescriptionWithNewLines}}
{{^microprofileServer}}
## Overview
This API client was generated by the [OpenAPI Generator](https://openapi-generator.tech) project.
[MicroProfile Rest Client](https://github.com/eclipse/microprofile-rest-client) is a type-safe way of calling
REST services. The generated client contains an interface which acts as the client, you can inject it into dependent classes.
{{/microprofileServer}}

{{#microprofileServer}}
## Overview
This server was generated by the [OpenAPI Generator](https://openapi-generator.tech) project.
The generated server contains an interface which acts as the server, you can inject it into the controller class.
This module is intended to provide additional server features, like accessing an operations response object, when multiple responses where specified.
{{/microprofileServer}}