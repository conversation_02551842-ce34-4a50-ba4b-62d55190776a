<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>{{groupId}}</groupId>
    <artifactId>{{artifactId}}</artifactId>
    <packaging>jar</packaging>
    <name>{{artifactId}}</name>
    <version>1.0.0-SNAPSHOT</version>

    <properties>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <kumuluzee.version>3.9.0</kumuluzee.version>
        <kumuluzee-rest.version>1.2.3</kumuluzee-rest.version>
        <kumuluzee-rest-client.version>1.4.1</kumuluzee-rest-client.version>
        <cxf-rt-rs-extension-providers.version>3.2.6</cxf-rt-rs-extension-providers.version>
        <junit-version>5.10.2</junit-version>
        <jersey-version>2.28</jersey-version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.kumuluz.ee</groupId>
                <artifactId>kumuluzee-bom</artifactId>
                <version>${kumuluzee.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <dependencies>
        <dependency>
            <groupId>com.kumuluz.ee</groupId>
            <artifactId>kumuluzee-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kumuluz.ee</groupId>
            <artifactId>kumuluzee-servlet-jetty</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kumuluz.ee</groupId>
            <artifactId>kumuluzee-jax-rs-jersey</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kumuluz.ee</groupId>
            <artifactId>kumuluzee-cdi-weld</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kumuluz.ee</groupId>
            <artifactId>kumuluzee-json-p-jsonp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kumuluz.ee</groupId>
            <artifactId>kumuluzee-json-b-yasson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.kumuluz.ee.rest-client</groupId>
            <artifactId>kumuluzee-rest-client</artifactId>
            <version>${kumuluzee-rest-client.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.cxf</groupId>
            <artifactId>cxf-rt-rs-extension-providers</artifactId>
            <version>${cxf-rt-rs-extension-providers.version}</version>
        </dependency>
        <dependency>
            <groupId>org.junit.jupiter</groupId>
            <artifactId>junit-jupiter-api</artifactId>
            <version>${junit-version}</version>
            <scope>test</scope>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <groupId>com.kumuluz.ee</groupId>
                <artifactId>kumuluzee-maven-plugin</artifactId>
                <version>${kumuluzee.version}</version>
                <executions>
                    <execution>
                        <id>package</id>
                        <goals>
                            <goal>repackage</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>

</project>