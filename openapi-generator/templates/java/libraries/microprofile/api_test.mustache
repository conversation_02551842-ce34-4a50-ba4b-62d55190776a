{{>licenseInfo}}

package {{package}};

{{#imports}}import {{import}};
{{/imports}}
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.Assertions;
{{#generateSpringBootApplication}}
import org.junit.jupiter.api.extension.ExtendWith;
import org.springframework.test.context.junit.jupiter.SpringExtension;
{{/generateSpringBootApplication}}

import org.eclipse.microprofile.rest.client.RestClientBuilder;

import java.net.URL;
import java.net.MalformedURLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 {{#appName}}
 * {{{.}}} Test
 *
 {{/appName}}
 * API tests for {{classname}}
 */
{{#generateSpringBootApplication}}
@ExtendWith(SpringExtension.class)
@SpringApplicationConfiguration(classes = SpringBootApplication.class)
@WebAppConfiguration
@IntegrationTest("server.port=0")
{{/generateSpringBootApplication}}
public class {{classname}}Test {

    private {{classname}} client;
    private String baseUrl = "http://localhost:9080";

    @BeforeEach
    public void setup() throws MalformedURLException {
        {{#microprofile3}}
        // TODO initialize the client
        {{/microprofile3}}
        {{^microprofile3}}
        client = RestClientBuilder.newBuilder()
                        .baseUrl(new URL(baseUrl))
                        .register(ApiException.class)
                        .build({{classname}}.class);
        {{/microprofile3}}
    }

    {{#operations}}{{#operation}}
    /**
     {{#summary}}
     * {{summary}}
     *
     {{#notes}}
     * {{.}}
     *
     {{/notes}}
     {{/summary}}
     * @throws ApiException
     *          if the Api call fails
     */
    @Test
    public void {{operationId}}Test() {
        // TODO: test validations
        {{#allParams}}
        {{^isFile}}{{{dataType}}} {{paramName}} = null;{{/isFile}}{{#isFile}}org.apache.cxf.jaxrs.ext.multipart.Attachment {{paramName}} = null;{{/isFile}}
        {{/allParams}}
        //{{^vendorExtensions.x-java-is-response-void}}{{#microprofileMutiny}}Uni<{{{returnType}}}>{{/microprofileMutiny}}{{^microprofileMutiny}}{{{returnType}}}{{/microprofileMutiny}} response = {{/vendorExtensions.x-java-is-response-void}}api.{{operationId}}({{#allParams}}{{paramName}}{{^-last}}, {{/-last}}{{/allParams}});
        //{{#returnType}}Assertions.assertNotNull(response);{{/returnType}}


    }
    {{/operation}}{{/operations}}
}
