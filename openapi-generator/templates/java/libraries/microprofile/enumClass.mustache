{{#withXml}}
@XmlType(name="{{datatypeWithEnum}}")
@XmlEnum({{dataType}}.class)
{{/withXml}}
{{^withXml}}
  {{#jsonb}}
  @JsonbTypeSerializer({{datatypeWithEnum}}.Serializer.class)
  @JsonbTypeDeserializer({{datatypeWithEnum}}.Deserializer.class)
  {{/jsonb}}
{{/withXml}}
  {{>additionalEnumTypeAnnotations}}public enum {{datatypeWithEnum}}{{#vendorExtensions.x-implements}}{{#-first}} implements {{{.}}}{{/-first}}{{^-first}}, {{{.}}}{{/-first}}{{/vendorExtensions.x-implements}} {

    {{#allowableValues}}
    {{#withXml}}
    {{#enumVars}}@XmlEnumValue({{#isInteger}}"{{/isInteger}}{{#isDouble}}"{{/isDouble}}{{#isLong}}"{{/isLong}}{{#isFloat}}"{{/isFloat}}{{{value}}}{{#isInteger}}"{{/isInteger}}{{#isDouble}}"{{/isDouble}}{{#isLong}}"{{/isLong}}{{#isFloat}}"{{/isFloat}}) {{name}}({{dataType}}.valueOf({{{value}}})){{^-last}}, {{/-last}}{{#-last}};{{/-last}}{{/enumVars}}
    {{/withXml}}
    {{^withXml}}
    {{#enumVars}}{{name}}({{^isUri}}{{dataType}}.valueOf({{/isUri}}{{{value}}}{{^isUri}}){{/isUri}}){{^-last}}, {{/-last}}{{#-last}};{{/-last}}{{/enumVars}}
    {{/withXml}}
    {{/allowableValues}}


    {{dataType}} value;

    {{datatypeWithEnum}} ({{dataType}} v) {
        value = v;
    }

    {{#jackson}}
    @JsonValue
    {{/jackson}}
    public {{dataType}} value() {
        return value;
    }

    @Override
    public String toString() {
        return String.valueOf(value);
    }

    {{#withXml}}
    public static {{datatypeWithEnum}} fromValue(String v) {
        for ({{{datatypeWithEnum}}}{{^datatypeWithEnum}}{{{classname}}}{{/datatypeWithEnum}} b : {{{datatypeWithEnum}}}{{^datatypeWithEnum}}{{{classname}}}{{/datatypeWithEnum}}.values()) {
            if (String.valueOf(b.value).equals(v)) {
                return b;
            }
        }
        {{#useNullForUnknownEnumValue}}return null;{{/useNullForUnknownEnumValue}}{{^useNullForUnknownEnumValue}}throw new IllegalArgumentException("Unexpected value '" + v + "'");{{/useNullForUnknownEnumValue}}
    }
    {{/withXml}}
    {{^withXml}}
    {{#jsonb}}
    public static final class Deserializer implements JsonbDeserializer<{{datatypeWithEnum}}> {
        @Override
        public {{datatypeWithEnum}} deserialize(JsonParser parser, DeserializationContext ctx, Type rtType) {
            for ({{{datatypeWithEnum}}}{{^datatypeWithEnum}}{{{classname}}}{{/datatypeWithEnum}} b : {{{datatypeWithEnum}}}{{^datatypeWithEnum}}{{{classname}}}{{/datatypeWithEnum}}.values()) {
                if (String.valueOf(b.value).equals(parser.getString())) {
                    return b;
                }
            }
            {{#useNullForUnknownEnumValue}}return null;{{/useNullForUnknownEnumValue}}{{^useNullForUnknownEnumValue}}throw new IllegalArgumentException("Unexpected value '" + parser.getString() + "'");{{/useNullForUnknownEnumValue}}
        }
    }

    public static final class Serializer implements JsonbSerializer<{{datatypeWithEnum}}> {
        @Override
        public void serialize({{datatypeWithEnum}} obj, JsonGenerator generator, SerializationContext ctx) {
            generator.write(obj.value{{#isUri}}.toASCIIString(){{/isUri}});
        }
    }
    {{/jsonb}}
    {{#jackson}}
    @JsonCreator
        public static {{{datatypeWithEnum}}}{{^datatypeWithEnum}}{{{classname}}}{{/datatypeWithEnum}} fromValue({{{dataType}}} value) {
            for ({{{datatypeWithEnum}}}{{^datatypeWithEnum}}{{{classname}}}{{/datatypeWithEnum}} b : {{{datatypeWithEnum}}}{{^datatypeWithEnum}}{{{classname}}}{{/datatypeWithEnum}}.values()) {
                if (b.value.equals(value)) {
                    return b;
                }
            }
            {{#isNullable}}return null;{{/isNullable}}{{^isNullable}}{{#enumUnknownDefaultCase}}{{#allowableValues}}{{#enumVars}}{{#-last}}return {{{name}}};{{/-last}}{{/enumVars}}{{/allowableValues}}{{/enumUnknownDefaultCase}}{{^enumUnknownDefaultCase}}throw new IllegalArgumentException("Unexpected value '" + value + "'");{{/enumUnknownDefaultCase}}{{/isNullable}}
        }
    {{/jackson}}
    {{/withXml}}
  }
