{{>licenseInfo}}
package {{package}};

{{#imports}}import {{import}};
{{/imports}}

import java.io.InputStream;
import java.io.OutputStream;
import java.util.List;
import java.util.Map;
import java.util.Set;
import {{rootJavaEEPackage}}.ws.rs.*;
import {{rootJavaEEPackage}}.ws.rs.core.Response;
import {{rootJavaEEPackage}}.ws.rs.core.MediaType;
{{^disableMultipart}}
import org.apache.cxf.jaxrs.ext.multipart.*;
{{/disableMultipart}}
{{#microprofileMutiny}}
import io.smallrye.mutiny.Uni;
{{/microprofileMutiny}}

{{#useBeanValidation}}
import {{rootJavaEEPackage}}.validation.constraints.*;
import {{rootJavaEEPackage}}.validation.Valid;
{{/useBeanValidation}}

import org.eclipse.microprofile.rest.client.annotation.RegisterProvider;
import org.eclipse.microprofile.rest.client.inject.RegisterRestClient;

{{#appName}}
/**
 * {{{appName}}}
 *
 {{#appDescription}}
 * <p>{{{.}}}
 *
 {{/appDescription}}
 */
{{/appName}}

{{^microprofileServer}}
@RegisterRestClient{{#configKey}}(configKey="{{configKey}}"){{/configKey}}{{#configKeyFromClassName}}{{#operations}}(configKey="{{configKey}}"){{/operations}}{{/configKeyFromClassName}}
{{/microprofileServer}}
@RegisterProvider(ApiExceptionMapper.class)
@Path("{{#useAnnotatedBasePath}}{{contextPath}}{{/useAnnotatedBasePath}}{{commonPath}}")
public interface {{classname}}  {
{{#operations}}
{{#operation}}

    {{#summary}}
    /**
     * {{summary}}
     *
     {{#notes}}
     * {{.}}
     *
     {{/notes}}
     {{#isDeprecated}}
     * @deprecated
     {{/isDeprecated}}
     */
    {{/summary}}
    {{#isDeprecated}}
    @Deprecated
    {{/isDeprecated}}
    @{{httpMethod}}
    {{#subresourceOperation}}@Path("{{{path}}}"){{/subresourceOperation}}
{{#hasConsumes}}
    @Consumes({ {{#consumes}}"{{{mediaType}}}"{{^-last}}, {{/-last}}{{/consumes}} })
{{/hasConsumes}}
{{#hasProduces}}
    @Produces({ {{#produces}}"{{{mediaType}}}"{{^-last}}, {{/-last}}{{/produces}} })
{{/hasProduces}}
{{^singleRequestParameter}}
    {{^vendorExtensions.x-java-is-response-void}}{{#microprofileServer}}{{> server_operation}}{{/microprofileServer}}{{^microprofileServer}}{{> client_operation}}{{/microprofileServer}}{{/vendorExtensions.x-java-is-response-void}}{{#vendorExtensions.x-java-is-response-void}}{{#microprofileMutiny}}Uni<Void>{{/microprofileMutiny}}{{^microprofileMutiny}}void{{/microprofileMutiny}}{{/vendorExtensions.x-java-is-response-void}} {{nickname}}({{#allParams}}{{>queryParams}}{{>pathParams}}{{>headerParams}}{{>bodyParams}}{{>formParams}}{{>cookieParams}}{{^-last}}, {{/-last}}{{/allParams}}) throws ApiException, ProcessingException;
{{/singleRequestParameter}}
{{#singleRequestParameter}}
    {{^vendorExtensions.x-java-is-response-void}}{{#microprofileMutiny}}Uni<{{{returnType}}}>{{/microprofileMutiny}}{{^microprofileMutiny}}{{{returnType}}}{{/microprofileMutiny}}{{/vendorExtensions.x-java-is-response-void}}{{#vendorExtensions.x-java-is-response-void}}{{#microprofileMutiny}}Uni<Void>{{/microprofileMutiny}}{{^microprofileMutiny}}void{{/microprofileMutiny}}{{/vendorExtensions.x-java-is-response-void}} {{nickname}}({{#hasNonBodyParams}}@BeanParam {{operationIdCamelCase}}Request request{{/hasNonBodyParams}}{{#bodyParams}}{{#hasNonBodyParams}}, {{/hasNonBodyParams}}{{>bodyParams}}{{/bodyParams}}) throws ApiException, ProcessingException;
    {{#hasNonBodyParams}}
    public class {{operationIdCamelCase}}Request {

        {{#queryParams}}
        private {{>queryParams}};
        {{/queryParams}}
        {{#headerParams}}
        private {{>headerParams}};
        {{/headerParams}}
        {{#pathParams}}
        private {{>pathParams}};
        {{/pathParams}}
        {{#formParams}}
        private {{>formParams}};
        {{/formParams}}
        {{#cookieParams}}
        private {{>cookieParams}};
        {{/cookieParams}}

        private {{operationIdCamelCase}}Request() {
        }

        public static {{operationIdCamelCase}}Request newInstance() {
            return new {{operationIdCamelCase}}Request();
        }

        {{#allParams}}
        {{^isBodyParam}}
        /**
         * Set {{paramName}}{{>formParamsNameSuffix}}
         * @param {{paramName}}{{>formParamsNameSuffix}} {{description}} ({{^required}}optional{{^isContainer}}{{#defaultValue}}, default to {{.}}{{/defaultValue}}{{/isContainer}}{{/required}}{{#required}}required{{/required}})
         * @return {{operationIdCamelCase}}Request
         */
        public {{operationIdCamelCase}}Request {{paramName}}{{>formParamsNameSuffix}}({{>queryParamsImpl}}{{>pathParamsImpl}}{{>headerParamsImpl}}{{>formParamsImpl}}{{>cookieParamsImpl}}) {
            this.{{paramName}}{{>formParamsNameSuffix}} = {{paramName}}{{>formParamsNameSuffix}};
            return this;
        }
        {{/isBodyParam}}
        {{/allParams}}
    }
    {{/hasNonBodyParams}}
{{/singleRequestParameter}}
{{/operation}}
}
{{/operations}}
