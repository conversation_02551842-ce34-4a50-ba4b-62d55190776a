{{>licenseInfo}}
package {{apiPackage}};

import {{rootJavaEEPackage}}.ws.rs.core.Response;

public class ApiException extends{{#useRuntimeException}} RuntimeException {{/useRuntimeException}}{{^useRuntimeException}} Exception {{/useRuntimeException}}{

  private static final long serialVersionUID = 1L;
  private Response response;

  public ApiException() {
    super();
  }

  public ApiException(Response response) {
    super("Api response has status code " + response.getStatus());
    this.response = response;
  }

  public Response getResponse() {
    return this.response;
  }
}
