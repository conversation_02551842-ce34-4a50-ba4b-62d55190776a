<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>{{groupId}}</groupId>
  <artifactId>{{artifactId}}</artifactId>
  <packaging>jar</packaging>
  <name>{{artifactId}}</name>
  {{#appDescription}}
  <description>{{.}}</description>
  {{/appDescription}}
  <version>{{artifactVersion}}</version>
  <build>
    <sourceDirectory>src/main/java</sourceDirectory>
    <plugins>
      <plugin>
        <groupId>org.jboss.jandex</groupId>
        <artifactId>jandex-maven-plugin</artifactId>
        <version>${jandex.maven.plugin.version}</version>
        <executions>
          <execution>
            <id>make-index</id>
            <goals>
              <goal>jandex</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-failsafe-plugin</artifactId>
        <version>${maven.failsafe.plugin.version}</version>
        <executions>
          <execution>
            <goals>
              <goal>integration-test</goal>
              <goal>verify</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>build-helper-maven-plugin</artifactId>
        <version>${build.helper.maven.plugin.version}</version>
        <executions>
          <execution>
            <id>add-source</id>
            <phase>generate-sources</phase>
            <goals>
              <goal>add-source</goal>
            </goals>
            <configuration>
              <sources>
                <source>src/gen/java</source>
              </sources>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <dependencies>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <version>${junit.version}</version>
      <scope>test</scope>
    </dependency>
{{#useBeanValidation}}
    <!-- Bean Validation API support -->
    <dependency>
        <groupId>jakarta.validation</groupId>
        <artifactId>jakarta.validation-api</artifactId>
        <version>${beanvalidation.version}</version>
        <scope>provided</scope>
    </dependency>
{{/useBeanValidation}}
    <!-- Eclipse MicroProfile Rest Client -->
    <dependency>
      <groupId>org.eclipse.microprofile.rest.client</groupId>
      <artifactId>microprofile-rest-client-api</artifactId>
      <version>${microprofile.rest.client.api.version}</version>
    </dependency>

    <!-- JAX-RS -->
    <dependency>
        <groupId>jakarta.ws.rs</groupId>
        <artifactId>jakarta.ws.rs-api</artifactId>
        <version>${jakarta.ws.rs.version}</version>
        <scope>provided</scope>
    </dependency>

    <dependency>
        <groupId>org.glassfish.jersey.ext.microprofile</groupId>
        <artifactId>jersey-mp-rest-client</artifactId>
        <version>${jersey.mp.rest.client.version}</version>
        <scope>test</scope>
    </dependency>
    <dependency>
        <groupId>org.apache.geronimo.config</groupId>
        <artifactId>geronimo-config-impl</artifactId>
        <version>${geronimo.config.impl.version}</version>
        <scope>test</scope>
    </dependency>

    {{^disableMultipart}}
    <dependency>
        <groupId>org.apache.cxf</groupId>
        <artifactId>cxf-rt-rs-extension-providers</artifactId>
        <version>${cxf.rt.rs.extension.providers.version}</version>
    </dependency>
    {{/disableMultipart}}
    {{#jsonb}}
    <dependency>
      <groupId>jakarta.json.bind</groupId>
      <artifactId>jakarta.json.bind-api</artifactId>
      <version>${jakarta.json.bind.version}</version>
    </dependency>
    <dependency>
        <groupId>jakarta.json</groupId>
        <artifactId>jakarta.json-api</artifactId>
        <version>${jakarta.json.version}</version>
    </dependency>
    <dependency>
        <groupId>jakarta.xml.bind</groupId>
        <artifactId>jakarta.xml.bind-api</artifactId>
        <version>${jakarta.xml.bind.version}</version>
    </dependency>
    <dependency>
        <groupId>com.sun.xml.bind</groupId>
        <artifactId>jaxb-core</artifactId>
        <version>${jaxb.core.version}</version>
    </dependency>
    <dependency>
        <groupId>com.sun.xml.bind</groupId>
        <artifactId>jaxb-impl</artifactId>
        <version>${jaxb.impl.version}</version>
    </dependency>
    {{/jsonb}}
    {{#jackson}}
    <!-- JSON processing: jackson -->
    <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-core</artifactId>
        <version>${jackson.version}</version>
    </dependency>
    <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-annotations</artifactId>
        <version>${jackson.version}</version>
    </dependency>
    <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
        <version>${jackson.version}</version>
    </dependency>
    {{#withXml}}
    <!-- XML processing with Jackson -->
    <dependency>
        <groupId>jakarta.xml.bind</groupId>
        <artifactId>jakarta.xml.bind-api</artifactId>
        <version>${jakarta.xml.bind.version}</version>
    </dependency>
    <dependency>
        <groupId>com.fasterxml.jackson.dataformat</groupId>
        <artifactId>jackson-dataformat-xml</artifactId>
        <version>${jackson.version}</version>
    </dependency>
    {{/withXml}}
    {{/jackson}}
    <dependency>
        <groupId>jakarta.activation</groupId>
        <artifactId>jakarta.activation-api</artifactId>
        <version>${jakarta.activation.version}</version>
    </dependency>
    <dependency>
        <groupId>com.fasterxml.jackson.datatype</groupId>
        <artifactId>jackson-datatype-jsr310</artifactId>
        <version>${jackson.jaxrs.version}</version>
    </dependency>
{{#useBeanValidationFeature}}
    <dependency>
        <groupId>org.hibernate</groupId>
        <artifactId>hibernate-validator</artifactId>
        <version>${hibernate.validator.version}</version>
    </dependency>
{{/useBeanValidationFeature}}
    <dependency>
        <groupId>jakarta.annotation</groupId>
        <artifactId>jakarta.annotation-api</artifactId>
        <version>${jakarta.annotation.version}</version>
        <scope>provided</scope>
    </dependency>
{{#microprofileMutiny}}
    <dependency>
        <groupId>io.smallrye.reactive</groupId>
        <artifactId>mutiny</artifactId>
        <version>${mutiny.version}</version>
    </dependency>
{{/microprofileMutiny}}
{{#useReflectionEqualsHashCode}}
    <!-- For equals and hashCode using reflection -->
    <dependency>
      <groupId>org.apache.commons</groupId>
      <artifactId>commons-lang3</artifactId>
      <version>${commons.lang3.version}</version>
    </dependency>
{{/useReflectionEqualsHashCode}}
  </dependencies>
  <repositories>
    <repository>
      <id>sonatype-snapshots</id>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
      <snapshots>
        <enabled>true</enabled>
      </snapshots>
    </repository>
  </repositories>
  <properties>
    <java.version>11</java.version>
    <maven.compiler.source>${java.version}</maven.compiler.source>
    <maven.compiler.target>${java.version}</maven.compiler.target>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

    <swagger.core.version>1.5.18</swagger.core.version>
    <jetty.version>9.2.9.v20150224</jetty.version>
    <junit.version>5.10.2</junit.version>
    <logback.version>1.5.13</logback.version>
{{#useBeanValidation}}
    <beanvalidation.version>3.0.1</beanvalidation.version>
{{/useBeanValidation}}
    <cxf.version>3.2.7</cxf.version>
    <jackson.jaxrs.version>2.17.1</jackson.jaxrs.version>
{{#jackson}}
    <jackson.version>2.17.1</jackson.version>
{{/jackson}}
    <jakarta.activation.version>2.1.0</jakarta.activation.version>
    <jakarta.annotation.version>2.0.0</jakarta.annotation.version>
    <jakarta.json.bind.version>3.0.0</jakarta.json.bind.version>
    <jakarta.json.version>2.0.1</jakarta.json.version>
    <jakarta.ws.rs.version>3.0.0</jakarta.ws.rs.version>
    <jakarta.xml.bind.version>3.0.1</jakarta.xml.bind.version>
    <microprofile.rest.client.api.version>{{microprofileRestClientVersion}}</microprofile.rest.client.api.version>
    <jersey.mp.rest.client.version>3.0.4</jersey.mp.rest.client.version>
    <geronimo.config.impl.version>1.2.3</geronimo.config.impl.version>
    <cxf.rt.rs.extension.providers.version>3.5.1</cxf.rt.rs.extension.providers.version>
    <jaxb.core.version>3.0.2</jaxb.core.version>
    <jaxb.impl.version>3.0.2</jaxb.impl.version>
    <hibernate.validator.version>7.0.4.Final</hibernate.validator.version>
    <jandex.maven.plugin.version>1.1.0</jandex.maven.plugin.version>
    <maven.failsafe.plugin.version>2.6</maven.failsafe.plugin.version>
    <build.helper.maven.plugin.version>1.9.1</build.helper.maven.plugin.version>
{{#microprofileMutiny}}
    <mutiny.version>1.10.0</mutiny.version>
{{/microprofileMutiny}}
{{#useReflectionEqualsHashCode}}
    <commons.lang3.version>3.17.0</commons.lang3.version>
{{/useReflectionEqualsHashCode}}
  </properties>
</project>
