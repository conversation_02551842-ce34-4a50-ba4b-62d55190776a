{"properties": {"name": "Shein", "icon": "icon/shein.png", "doc": "${doc}", "id": "shein", "tags": ["SaaS"]}, "configOptions": {"pdkExpansion": [], "node": {"type": "object", "properties": {"logCompile": {"type": "boolean", "title": "${open_log_compile}", "x-decorator": "FormItem", "x-component": "Switch", "default": false, "x-decorator-props": {"colon": false, "tooltip": "${log_compile_tip}"}, "x-index": 10}, "filter": {"type": "String", "title": "Filter and show Record", "x-decorator": "FormItem", "x-component": "Input", "default": "", "x-index": 999998, "x-display": "hidden"}}}, "connection": {"type": "object", "properties": {"openKeyId": {"type": "String", "title": "${con_open_key_id}", "x-decorator": "FormItem", "x-component": "Input", "x-index": 10, "required": true, "x-decorator-props": {"tooltip": "${tip_user_tags_openId}"}}, "secretKey": {"type": "String", "title": "${con_secret_key}", "x-decorator": "FormItem", "x-component": "Input", "x-index": 20, "required": true, "x-decorator-props": {"tooltip": "${tip_user_tags_key}"}}, "doMain": {"type": "String", "title": "${con_do_main}", "x-decorator": "FormItem", "x-component": "Input", "x-index": 30, "default": "https://openapi.sheincorp.cn", "x-decorator-props": {"tooltip": "${tip_user_tags_url}"}, "x-display": "hidden", "required": true}, "tableType": {"type": "String", "default": "CSV", "title": "${tableType}", "x-decorator": "FormItem", "x-component": "Select", "x-display": "hidden", "enum": [{"label": "${table_csv}", "value": "CSV"}, {"label": "${table_document}", "value": "Document"}], "x-index": 9999, "required": false}}}}, "messages": {"default": "en_US", "en_US": {"doc": "doc/shein_en_US.md", "con_open_key_id": "Open Key ID", "con_secret_key": "Secret Key", "con_do_main": "Environmental address", "tip_user_tags_openId": "You need to contact the corresponding SHEIN specific system contact person to obtain Open Key ID", "tip_user_tags_key": "You need to contact the corresponding SHEIN specific system contact person to obtain Secret Key", "tip_user_tags_url": "Need to contact SHIEN's corresponding contact person to obtain Environmental address", "tableType": "Data type", "table_csv": "CSV", "table_document": "Document", "open_log_compile": "Enable data comparison ", "log_compile_tip": "After the switch is turned on, the original data in front of the data field displayed in the Info level log and the flattened data after processing can be displayed in the log list"}, "zh_CN": {"doc": "doc/shein_zh_CN.md", "con_open_key_id": "Open Key ID", "con_secret_key": "Secret Key", "con_do_main": "环境地址", "tip_user_tags_openId": "需要联系对应SHEIN具体系统对接人获取Open Key ID", "tip_user_tags_key": "需要联系对应SHEIN具体系统对接人获取Secret Key", "tip_user_tags_url": "需要联系SHIEN相应的对接人获取环境地址", "tableType": "Data type", "table_csv": "CSV", "table_document": "文档模式", "open_log_compile": "开启数据比对", "log_compile_tip": "开启开关后可在日志列表中以Info级别日志的展示数据字段前的原始数据和处理后的拍平格式数据"}, "zh_TW": {"doc": "doc/shein_zh_TW.md", "con_open_key_id": "Open Key ID", "con_secret_key": "Secret Key", "con_do_main": "環境地址", "tip_user_tags_openId": "需要聯系對應SHEIN具體系統對接人獲取Open Key ID", "tip_user_tags_key": "需要联系对应SHEIN具体系统对接人获取Secret Key", "tip_user_tags_url": "需要聯系SHIEN相應的對接人獲取環境地址", "tableType": "Data type", "table_csv": "CSV", "table_document": "檔案模式", "open_log_compile": "開啟數據比對", "log_compile_tip": "開啟開關後可在日誌清單中以Info級別日誌的展示數據欄位前的原始數據和處理後的拍平格式數據 "}}, "dataTypes": {"Object": {"to": "TapMap"}, "Null": {"to": "TapArray"}, "Number": {"precision": [1, 1000], "scale": [0, 1000], "fixed": true, "preferPrecision": 20, "preferScale": 8, "priority": 1, "to": "TapNumber"}, "DateTime0": {"range": ["1000-01-01 00:00:00", "9999-12-31 23:59:59"], "defaultFraction": 3, "pattern": "yyyy-MM-dd HH:mm:ss", "priority": 1, "to": "TapDateTime"}, "DateTime": {"range": ["1000-01-01 00:00:00.000", "9999-12-31 23:59:59.999"], "pattern": "yyyy-MM-dd hh:mm:ss.sss", "fraction": [0, 9], "defaultFraction": 3, "withTimeZone": false, "priority": 2, "to": "TapDateTime"}, "Long": {"bit": 64, "priority": 10, "value": [-9223372036854775808, 9223372036854775807], "scale": 0, "to": "TapNumber"}, "Boolean": {"to": "TapBoolean"}, "String": {"byte": 1024, "priority": 1, "defaultByte": 512, "preferByte": 1024, "to": "TapString"}, "Array": {"to": "TapArray"}, "Float": {"to": "TapNumber", "bit": 64, "scale": [0, 6], "fixed": false}, "Integer": {"bit": 32, "to": "TapNumber", "precision": 9, "priority": 1, "value": [-999999999, 999999999], "scale": 0}, "Money": {"to": "TapNumber", "precision": [1, 65], "value": [-999999999.99, 999999999.99], "scale": 2}}}