# Capped Collection 增强实现

## 概述

本次修改扩展了 `CustomMongoConfig` 类的功能，使其能够处理已存在的集合，将其转换为 capped 集合或更新 capped 集合的属性。

## 主要功能

### 1. 集合不存在时
- 创建新的 capped 集合（原有功能保持不变）

### 2. 集合已存在且不是 capped 集合时
- 使用 MongoDB 的 `convertToCapped` 命令将普通集合转换为 capped 集合
- 设置指定的 `maxLength` 和 `maxMemory` 属性

### 3. 集合已存在且是 capped 集合时
- 获取现有集合的属性（maxSize, max）
- 与注解中定义的属性进行比较
- 如果属性相同，不做任何处理
- 如果属性不同，重建集合（会导致数据丢失，但会创建备份）

## 核心方法

### `handleCappedCollection(String collectionName, CappedCollection annotation)`
- 主要的处理入口，根据集合是否存在调用相应的处理方法

### `createCappedCollection(String collectionName, CappedCollection annotation)`
- 创建新的 capped 集合

### `updateExistingCollection(String collectionName, CappedCollection annotation)`
- 处理已存在的集合，检查是否需要转换或更新

### `getCollectionStats(String collectionName)`
- 使用 MongoDB 的 `collStats` 命令获取集合统计信息
- 返回 `CollectionStats` 对象，包含 capped 状态和相关属性

### `convertToCappedCollection(String collectionName, CappedCollection annotation)`
- 将普通集合转换为 capped 集合
- 使用 MongoDB 的 `convertToCapped` 命令

### `recreateCappedCollection(String collectionName, CappedCollection annotation)`
- 重建 capped 集合（当属性需要修改时）
- 创建备份集合，然后重新创建原集合
- **注意：此操作会导致数据丢失**

### `needsUpdate(CollectionStats stats, CappedCollection annotation)`
- 比较现有集合属性与注解定义的属性
- 判断是否需要更新

## 重要注意事项

1. **数据丢失风险**：当需要修改 capped 集合的属性时，由于 MongoDB 不支持直接修改 capped 集合的大小限制，需要重建集合，这会导致数据丢失。系统会创建备份集合，但需要手动处理。

2. **日志记录**：所有操作都有详细的日志记录，包括警告和错误信息。

3. **异常处理**：所有方法都有适当的异常处理，确保单个集合的处理失败不会影响其他集合。

4. **向后兼容**：修改完全向后兼容，原有的集合创建功能保持不变。

## 使用示例

```java
@Document("ApiCall1")
@CappedCollection(maxLength = 1024L * 1024L * 1024L, maxMemory = 1000L)
public class ApiCallEntity extends BaseEntity {
    // 实体定义
}
```

当应用启动时，系统会：
1. 检查 "ApiCall1" 集合是否存在
2. 如果不存在，创建新的 capped 集合
3. 如果存在但不是 capped，转换为 capped 集合
4. 如果存在且是 capped，检查属性是否匹配，不匹配则更新

## 测试

创建了基本的单元测试 `CustomMongoConfigTest.java` 来验证核心逻辑。

## 部署建议

1. 在生产环境部署前，建议先在测试环境验证
2. 对于包含重要数据的 capped 集合，建议在修改属性前手动备份数据
3. 监控日志输出，特别是警告信息
