from datetime import datetime
data = [
    {"CUSTOMER_ID": "C1",   "CITY": "Deutschlandsberg",  "COUNTRY_CODE": "AT", "DATE_OF_BIRTH": datetime.strptime("1963-05-21T00:00:00Z", "%Y-%m-%dT%H:%M:%SZ"), "EMAIL": "<EMAIL>",     "FIRST_NAME": "Greta",   "GENDER": "F", "JOB": "Verkäufer / Verkäuferin",     "LAST_NAME": "<PERSON><PERSON><PERSON>",      "MARITAL_STATUS": "SINGLE",  "NATIONALITY": "Austria", "NUMBER_CHILDREN": 3, "PHONE": "+43-7509-xxxx",   "STREET": "Karlingergasse 419", "ZIP": "2926"},
    {"CUSTOMER_ID": "C2",   "CITY": "Hall in Tirol",     "COUNTRY_CODE": "AT", "DATE_OF_BIRTH": datetime.strptime("1961-01-25T00:00:00Z", "%Y-%m-%dT%H:%M:%SZ"), "EMAIL": "<EMAIL>",    "FIRST_NAME": "Bianca",  "GENDER": "F", "JOB": "Ingenieur / Ingenieurin",     "LAST_NAME": "Kozakiewicz", "MARITAL_STATUS": "MARRIED", "NATIONALITY": "Austria", "NUMBER_CHILDREN": 4, "PHONE": "+43-2359-xxxx",   "STREET": "Kürschnergasse 343", "ZIP": "9904"},
    {"CUSTOMER_ID": "C3",   "CITY": "Schwabmünchen",     "COUNTRY_CODE": "DE", "DATE_OF_BIRTH": datetime.strptime("1961-03-20T00:00:00Z", "%Y-%m-%dT%H:%M:%SZ"), "EMAIL": "<EMAIL>",     "FIRST_NAME": "Xanthia", "GENDER": "F", "JOB": "Ingenieur / Ingenieurin",     "LAST_NAME": "Huff",        "MARITAL_STATUS": "MARRIED", "NATIONALITY": "Germany", "NUMBER_CHILDREN": 0, "PHONE": "(09895) 222xxxx", "STREET": "Bundesautobahn 532", "ZIP": "72777"},
    {"CUSTOMER_ID": "C999", "CITY": "Eggesin",           "COUNTRY_CODE": "DE", "DATE_OF_BIRTH": datetime.strptime("1957-11-04T00:00:00Z", "%Y-%m-%dT%H:%M:%SZ"), "EMAIL": "<EMAIL>", "FIRST_NAME": "Meinrad", "GENDER": "M", "JOB": "Briefträger / Briefträgerin", "LAST_NAME": "Hartmann",    "MARITAL_STATUS": "SINGLE",  "NATIONALITY": "Germany", "NUMBER_CHILDREN": 0, "PHONE": "+49-150-384xxxx", "STREET": "Mehringdamm 234",    "ZIP": "08856"},
]
