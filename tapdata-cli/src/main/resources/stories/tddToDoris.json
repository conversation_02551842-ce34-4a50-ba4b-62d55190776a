{"id": "dag1", "nodes": [{"connectionConfig": {}, "table": {"name": "tdd-table", "id": "tdd-table"}, "id": "s1", "pdkId": "tdd-source", "group": "io.tapdata.connector", "type": "Source", "version": "1.1-SNAPSHOT"}, {"connectionConfig": {"host": "***************", "port": "9030", "database": "example_db", "user": "test", "password": "test"}, "table": {"name": "emptyTable2", "id": "emptyTable2"}, "id": "t2", "pdkId": "doris", "group": "io.tapdata.connector.doris", "type": "Target", "version": "1.0-SNAPSHOT"}], "dag": [["s1", "t2"]], "jobOptions": {"queueSize": 100, "queueBatchSize": 100, "actionsBeforeStart": ["dropTable", "createTable"]}}