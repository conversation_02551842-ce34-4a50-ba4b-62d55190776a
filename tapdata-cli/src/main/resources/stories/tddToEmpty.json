{"id": "dag1", "nodes": [{"connectionConfig": {}, "table": {"name": "tdd-table", "id": "tdd-table"}, "id": "s1", "pdkId": "tdd-source", "group": "io.tapdata.connector", "type": "Source", "version": "1.0-SNAPSHOT"}, {"connectionConfig": {}, "table": {"name": "empty-table2", "id": "empty-table2"}, "id": "t2", "pdkId": "<PERSON><PERSON><PERSON><PERSON><PERSON>get", "group": "io.tapdata.connector.empty", "type": "Target", "version": "1.1-SNAPSHOT"}], "dag": [["s1", "t2"]], "jobOptions": {"queueSize": 100, "queueBatchSize": 100, "actionsBeforeStart": ["dropTable", "createTable"]}}