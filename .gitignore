##################general file format
.classpath
.settings
.project
.npm
.config
.yarnrc
.DS_Store
.vscode
*.log
*.iso
*.log
*.lock
.idea
*.sublime-project
*.sublime-workspace
.stackname
*.pyc
.env
.venv
*.lock
.cache
*.iml
connector/agent.yml
.agentSingletonLock
#######################by file name




###################folder
*/.cache

mongoData/
mongounit/

iengine/logs/tapdata-agent.log

demo/mysql/logs/
demo/oracle/data/
dataset/connector/

logs/archive/
logs/connector/archive
logs/mongo
test/logs

transformer/logs/
backend/client/
backend/tapdata-management
localpkg/

/tdd-logs


target/
/tapdata-web.log.*
/.audit.json
logs/*
api-gateway/.*
/api-gateway/api-gateway.jar
/build/agent/tapdata-agent-*.tar.gz
apig/
/auto-test/config.yaml

dump
dev.sh
connector/connector-common/.*
connector/connector-manager/.*
connector/debezium/debezium-connector-mongodb/.*
connector/*/.factorypath
connector/*/*/.factorypath
connector/logs/*
connector/connector-manager/src/main/resources/json/DataFlow.json
/start_api-gateway.bat
/start_backend.bat
/start_fornend.bat
/start_service.bat
/test.yml

/demos
**/*.ipr
**/*.iws
/sphinx-docs/
/sphinx-docs/**
/demo/sybase/data/

/license/src/main/resources/keystore/password

agent.yml
backend/deploy/
.register

build_profile.js
/build/version/node_modules/
/build/version/package-lock.json
/backend/package-lock.json
.agentStartMsg
connector/tap-running/
connector/dist/
dist/
tap-running/
tap-dumping/
cacheData/
output/
/plugin-kit/connector-archetypes/source-connector-archetype/target/
/tdd-logs/
!/plugin-kit/tapdata-pdk-api/src/main/java/io/tapdata/pdk/apis/functions/connector/target/
!/tapdata-cli/src/main/java/io/tapdata/pdk/tdd/tests/target

temp/
tmp_dist/
tap_table_ehcache/
**/logs/
imap-cache-data/
CacheObserveLogs/
/node_modules/
/plugin-kit/tapdata-modules/tapdata-storage-module/tap_storage_test/
partition_storage/
/manager/tm/src/main/resources/test3/
null/

.agentSingletonLock
fileObserveLogAppenderV2/
**/pluginKit.properties


iengine/test
iengine/**/debug_data
iengine/**/cache


**/.jython_cache/
/*.dylib
py-lib
sybase-poc/
sybase-poc-temp/
.tmStartMsg.json

run-resources