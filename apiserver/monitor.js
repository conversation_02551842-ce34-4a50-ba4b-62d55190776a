const log = require('./dist').log.monitor;
const axios = require('axios');
// const appConfig = require('./config');
const hashCode = require('hashcode').hashCode;
const path = require('path');
const fs = require('fs');
const makeDir = require('make-dir');
const { getToken, removeToken } = require('./tapdata');
const tapdata = require('./tapdata');
const Conf = require('conf');
const config = new Conf();
const cpus = require('os').cpus().length;


/**
 * 最后配置信息的 hashCode，用于比较配置文件是否更新
 * @type {null}
 */
let lastHashCode = null,
	intervalId;

/**
 * 加载配置文件
 */
const
	__listeners = {},
	loadConfig = function (token) {
		const configWorkCount = config.get('api_worker_count') || 0;
		const workerCount = configWorkCount === 0 ? cpus : configWorkCount;
		const url = config.get('tapDataServer.url') + '/api/modules/apiDefinition';
		log.debug('download load config from server ' + url);
		const processId = config.get('reportData.process_id');
		axios.get(`${url}?access_token=${token}&code=${lastHashCode}&workerCount=${workerCount}&processId=${processId}`, {
			timeout: 5000,
			headers: {
				'Accept': 'application/json'
			}
		}).then(response => {
			let body = response.data;
			try{
				if(typeof body === 'string') {
					body = JSON.parse(body);
				}
				if(body.data && body.data.connections){
					for(let x in body.data.connections){
						if(body.data.connections[x].database_type === 'Tidb' || body.data.connections[x].database_type === 'Doris'){
							body.data.connections[x].database_type = 'mysql';
						}
					}
				}
			}catch (e) {
				log.error('parse config error: \n', e);
				return;
			}
			if("ok" === body.code) {
				let newHashCode = body.data.version;

        //Scan and update the roles of the API every time
        let mapping = config.get("apiRolesMapping")
				const apiIds = new Set(body.data && body.data.apis ? body.data.apis.map(a => a.id) : []);
				Object.keys(mapping).forEach(k => {
					if (!apiIds.has(k)) {
						delete mapping[k]
					}
				});
				if (body.data && body.data.apis) {
					body.data.apis.forEach(a => {
						if (a.paths) {
							a.paths.forEach(p => {
								if (p.acl) {
									mapping[a.id] = p.acl;
									delete p.acl
								}
							})
						}
					});
				}
				config.set("apiRolesMapping", mapping);
				config.set("textEncryptionRules", body.data.textEncryptionRules ? body.data.textEncryptionRules : {})

				const workInfo = body.data ? (body.data.workerInfo || []) : [];
				config.set("apiWorkInfo", workInfo);

				delete body.data.workerInfo;
        const bodyStr = JSON.stringify(body.data);
				log.debug('download config success.');

				const trimmedBody = bodyStr.trim();
				if(newHashCode === null || newHashCode === undefined){
					newHashCode = hashCode().value(trimmedBody);
				}
				/*if( ! (/^\{.*\}$/.test(body) || /^\[.*\]$/.test(body)) ){
                    log.error('the configuration file is not in the expected JSON format.', body);
                    return;
                }*/

				//  计算 hashCode 比较是否有修改
				//hashCode().value(body);
				// log.info(`old config hash code: ${lastHashCode}, new config hash code: ${newHashCode}`);
				if (!lastHashCode || !newHashCode ||newHashCode.toString() !== lastHashCode.toString()) {
					if(newHashCode) {
						lastHashCode = newHashCode.toString();
					}

					log.info('api config is changed, cache remote config to local.');
					let config = JSON.parse(trimmedBody);
					// 保存到本地缓存目录
					fs.writeFileSync(getCacheConfig(), JSON.stringify(config) + "\n");
					// 保存到本地缓存目录
					if(newHashCode) {
						fs.writeFileSync(getCacheConfig() + '.version', lastHashCode.toString());
					}
					try {
						for(let x in config.connections){
							config.connections[x].database_type = config.connections[x].database_type.toLowerCase().replace(' ','');
						}
						// 通知配置文件更新了
						const msg = {
							type: 'changed',
							data: config
						};
						if (__listeners['message']) {
							__listeners['message'].forEach((l) => {
								if (typeof l === 'function')
									l(msg);
							})
						}
					} catch (e) {
						log.error('parse config error: \n', e);
					}
				}
			}else{
				log.error('get config error: \n', body);
			}
		}).catch(error => {
			if (error.response) {
				if (error.response.status === 401 || error.response.status === 403) {
					console.error('Access token Expired');
					removeToken();
				} else {
					log.error('get config error: \n', error.response.data);
				}
			} else {
				log.error('download config fail.', error.message);
			}
		});
	},

	/**
	 * 系统启动时，初始化一些配置信息
	 * @private
	 */
	__init = function (cb) {
		const localConfigFilePath = getCacheConfig()+'.version';
		if (fs.existsSync(localConfigFilePath)) {
			lastHashCode = fs.readFileSync(localConfigFilePath).toString();
		}
		cb();
	},

	/**
	 * 获取本地缓存配置文件路径
	 */
	getCacheConfig = function () {

		const cacheFilePath = config.get('apiCache').startsWith('/') ? config.get('apiCache') : path.join(__dirname, config.get('apiCache'));
		const dir = path.dirname(cacheFilePath);
		if (!fs.existsSync(dir)) {
			log.info(`create cache dir ${dir}`);
			makeDir.sync(dir);
		}

		return path.resolve(cacheFilePath);
	};

exports.on = function (type, listener) {
	if (!__listeners[type])
		__listeners[type] = [];
	log.info('register listener on ' + type);
	__listeners[type].push(listener);
};
exports.start = function () {
	__init(() => {
		intervalId = setInterval(() => {
			getToken(token => {
				if (token) {
					loadConfig(token);
				}
			})

		}, config.get('intervals'));
	});
};
exports.stop = function () {
	if (intervalId)
		clearInterval(intervalId);
};

tapdata.on('defaultLimit:changed', (newVal, oldVal) => {
	log.info('defaultLimit is changed, new value is ' + newVal + ' and old value is ' + oldVal);
	config.set('defaultLimit', Number(newVal) || 10);
	lastHashCode = null;//regenerate code
});
tapdata.on('maxLimit:changed', (newVal, oldVal) => {
	log.info('maxLimit is changed, new value is ' + newVal + ' and old value is ' + oldVal);
	config.set('maxLimit', Number(newVal) || 0); // 0 not max limit
	lastHashCode = null;//regenerate code
});
tapdata.on('audioLogEncryption:changed', (newVal, oldVal) => {
	log.info('audioLogEncryption is changed, new value is ' + newVal + ' and old value is ' + oldVal);
	config.set('audioLogEncryption', 'true' === newVal || true === newVal);
	lastHashCode = null;//regenerate code
});

tapdata.on('accessTimeout:changed', (newVal, oldVal) => {
	log.info('accessTimeout is changed, new value is ' + newVal + ' and old value is ' + oldVal);
	config.set('accessTimeout', Number(newVal) || 30000);
	lastHashCode = null;//regenerate code
});