import * as pidusage from 'pidusage';
import {log} from '../log';

const Conf = require('conf');
const config = new Conf();

export interface ProcessStat {
  pid: number;
  cpu: number;
  memory: number;
}

export interface ServiceUsage {
  masterPid: number,
  pids: number[];
  cpu: number;
  memory: number;
  details: ProcessStat[];
  timestamp: number;
}

export class ServiceMonitor {
  private interval: number;
  private timer: NodeJS.Timeout | null = null;

  constructor(interval: number = 10000) {
    this.interval = interval;
  }

  getPids(): number[] {
    const systemPids: number[] = config.get('SYSTEM_PIDS') || [];
    config.set('SYSTEM_PIDS', systemPids);
    return systemPids;
  }

  /** 获取 CPU/内存使用情况 */
  async getUsage(): Promise<ServiceUsage> {
    const pids: number[] = this.getPids();
    const statsArr = await Promise.all(pids.map(pid => this.safePidusage(pid, log)));
    const details: ProcessStat[] = [];
    for (let i = 0; i < pids.length; i++) {
      const s = statsArr[i];
      if (s) {
        details.push({pid: pids[i], cpu: s.cpu, memory: s.memory});
      }
    }

    return {
      masterPid: pids[0],
      pids: pids,
      cpu: details.reduce((sum, d) => sum + d.cpu, 0),
      memory: details.reduce((sum, d) => sum + d.memory, 0),
      details,
      timestamp: Date.now(),
    };
  }

  start(callback: (usage: ServiceUsage) => void) {
    if (this.timer) {
      this.stop();
    }
    this.timer = setInterval(async () => {
      try {
        log.app.info('ServiceMonitor info');
        const usage = await this.getUsage();
        callback(usage);
      } catch (err) {
        //@ts-ignore
        log.app.error('ServiceMonitor error:', err.stack);
      }
    }, this.interval);
  }

  stop() {
    if (this.timer) {
      clearInterval(this.timer);
      this.timer = null;
    }
  }

  async safePidusage(pid: number, log: any): Promise<any | null> {
    try {
      return await pidusage(pid);
    } catch (err: any) {
      if (err.code === 'ENOENT') {
        log.app?.warn?.(`PID ${pid} not exists or no longer exists`);
      } else {
        log.app?.error?.(`Failed to get usage for PID ${pid}: ${err.message}`);
      }
      return null;
    }
  }
}