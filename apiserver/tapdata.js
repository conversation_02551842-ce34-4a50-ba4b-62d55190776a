/**
 * <AUTHOR>
 * @date 3/31/19
 * @description
 */
const axios = require('axios');
const EventEmitter = require('events');
const eventEmitter = new EventEmitter();
const cluster = require('cluster');
const Conf = require('conf');
const config = new Conf();

let token = null;
const getToken = function (cb) {
	cb = cb || function () { };
	if (token) {
		cb(token);
	} else {
		const formData = new URLSearchParams();
		formData.append('accesscode', config.get('tapDataServer.accessCode'));

		axios.post(config.get('tapDataServer.url') + '/api/users/generatetoken', formData, {
			headers: {
				'Content-Type': 'application/x-www-form-urlencoded'
			},
			timeout: 5000
		}).then(response => {
			const body = response.data;
			let result = body;
			if(typeof body === 'string') {
				try{
					result = JSON.parse(body);
				}catch (e) {
					console.error('parse config error: \n', e);
					cb(false);
					return;
				}
			}
			if(result.code === "ok") {
				token = result.data.id;
				cb(token);
				console.log('Get access token success,', JSON.stringify(result));
				if (result.data.ttl)
					setTimeout(() => {
						token = null;
					}, (result.data.ttl - 10000) * 1000) // 提前10分钟获取token
			}else{
				console.error('Get access token error,', JSON.stringify(result));
				cb(false)
			}
		}).catch(error => {
			if (error.response) {
				console.error('Get access token error,', error.response.data);
			} else {
				console.error('Get access token error', error.message);
			}
			cb(false);
		})
	}
},
	removeToken = function () {
		token = null;
	};

/**
 * 检查是否启用 load schema 功能
 */
const checkEnableLoadSchemaFeature = function (cb) {
	getToken(token => {
		let params = {
			'filter[where][worker_type][in][0]': 'connector',
			'filter[where][worker_type][in][1]': 'transformer',
			'filter[where][ping_time][gte]': new Date().getTime() - 60000
		};
		axios.get(config.get('tapDataServer.url') + '/api/Workers', {
			params: {
				...params,
				access_token: token
			},
			timeout: 5000,
			headers: {
				'Accept': 'application/json'
			}
		}).then(response => {
			const body = response.data;
			if(body){
				if("ok" === body.code) {
					const data = body.data;
					if (data && data.length > 0) {
						console.log('exists process connector or transformer, disable load schema feature');
						cb(false);
					} else {
						console.log('not exists process connector or transformer, enable load schema feature');
						cb(true);
					}
				} else {
					console.error('get connector worker process error: \n', body.msg);
					cb(false);
				}
			}else{
				console.error('get connector worker process error: no response body');
				cb(false);
			}
		}).catch(error => {
			if (error.response) {
				if (error.response.status === 401 || error.response.status === 403) {
					console.error('Access token Expired');
					removeToken();
				} else {
					console.error('get connector worker process error: \n', error.response.data);
				}
			} else {
				console.error('get connector worker process fail.', error.message);
			}
			cb(false);
		});
	});
};

// const settingCache = {};
const loadNewSettings = function () {
	getToken(token => {
		if (token) {
			let params = {
				filter: '{"where":{"category" :"ApiServer"}}',
				access_token: token
			};
			axios.get(config.get('tapDataServer.url') + '/api/Settings', {
				params: params,
				timeout: 5000,
				headers: {
					'Accept': 'application/json'
				}
			}).then(response => {
				const body = response.data;
				if(body) {
					if("ok" === body.code) {
						const data = body.data;
						if (data && data.length > 0) {

							data.forEach(setting => {
								let key = setting.key;
								let oldVal = config.get(key); //settingCache[key] || appConfig[key];
								let newVal = setting.value;
								if (String(oldVal) !== String(newVal)) { // setting changed
									eventEmitter.emit(key + ':changed', newVal, oldVal);
									//appConfig[key] = newVal;
									config.set(key, newVal);
									// settingCache[key] = newVal;
								}
							});

						} else {
							console.log('not found api server settings from backend.');
						}
					}
				}
			}).catch(error => {
				if (error.response) {
					if (error.response.status === 401 || error.response.status === 403) {
						console.error('Access token Expired');
						removeToken();
					} else {
						console.error('get settings from backend fail.', error.response.data);
					}
				} else {
					console.error('get settings from backend fail.', error.message);
				}
			});
		}
	});
};

if (cluster.isMaster) {
	console.log('check backend settings change in main process.');
	// load new settings for api server.
	setInterval(loadNewSettings, 5000);
}

module.exports = eventEmitter;
module.exports.getToken = getToken;
module.exports.removeToken = removeToken;
module.exports.checkEnableLoadSchemaFeature = checkEnableLoadSchemaFeature;
