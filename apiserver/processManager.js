import Conf from 'conf';
const config = new Conf();

export function addPid(pid) {
  //console.log(`addPid: ${pid}`);
  const systemPids = pids();
  if (pid && systemPids.indexOf(pid) === -1) {
    systemPids.push(pid);
  }
  config.set("SYSTEM_PIDS", systemPids);
}

export function pids() {
  const systemPids = config.get("SYSTEM_PIDS") || [];
  config.set("SYSTEM_PIDS", systemPids);
  return systemPids;
}

export function removePid(pid) {
  if (!pid) {
    return
  }
  const systemPids = pids();
  const indexOf = systemPids.indexOf(pid);
  if (indexOf === -1) {
    return;
  }
  systemPids.splice(indexOf, 1);
  config.set("SYSTEM_PIDS", systemPids);
}