const report = require('./report');
const reportApiCallStats = require('./reportApiCallStats');
let timer;
process.on('message', function(workerStatus){
  if(workerStatus === 'ping'){
    if(timer){
      try {
        clearTimeout(timer);
        timer = null;
      }catch (e) {}
    }
    return;
  }
  report.setStatus({
    worker_status: workerStatus
  });
});

setInterval(()=>{
  try {
    process.send('ping');
  }catch (e) {
    reportApiCallStats.stop(true);
  }
  timer = setTimeout(()=>{
    reportApiCallStats.stop(true);
  },5000);
},10000)