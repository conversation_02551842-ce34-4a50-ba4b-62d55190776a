const cluster = require('cluster');
const log = require('./dist').log.app;
const Conf = require('conf');
const {addPid} = require('./processManager');
const config = new Conf();

let appWorker = null;

/**
 * 工作进程
 */
const workerRun = function () {
	// const appConfig = require('./config');
	const application = require('./dist');
	// const report = require('./report');

	// Run the application
	const configm = {
		rest: {
			port: config.get('port') || +process.env.PORT || 3030,
			host: config.get('host') || process.env.HOST || 'localhost',
			openApiSpec: {
				// useful when used with OASGraph to locate your application
				setServersFromRequest: true,
			},
		},
	};
	application.main(configm, (result) => {
		if (result && typeof process.send === 'function')
			process.send({
				type: 'started',
			});
	}).catch(err => {
		log.error('Cannot start the application.', err);
		process.exit(1);
	});
},

  /**
   * 主进程
   */
	startWorker = function () {
		// Monitor parent process messages
		if (!appWorker) {
			appWorker = cluster.fork();
			if (appWorker.process && appWorker.process.pid) {
				addPid(appWorker.process.pid);
			}
			log.warn('The main process starts working');
		} else {
			log.warn('The main process already exists, no need to restart');
			return
		}
		let worker = appWorker;
		worker.on('message', (event) => {
			log.debug(`which pid? app_cluster.js:43: ${process.pid} and event: ${JSON.stringify(event)}`);
			if (event && event.type === 'started') {
				process.send({
					type: 'status',
					data: 'running'
				});
			}
		});
	};

if (cluster.isMaster) {
	log.debug(`master process.pid@app_cluster.js:53: ${process.pid}`);
	startWorker();
	process.on('message', (event) => {
		if (event && event.type === 'restart')
			startWorker();
	})
} else {
	log.debug(`slave process.pid@app_cluster.js:60: ${process.pid}`);
	workerRun();
}

