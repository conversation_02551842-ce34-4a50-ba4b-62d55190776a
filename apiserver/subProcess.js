const log = require('./dist').log.app;
const configMonitor = require('./monitor');
const TapDataCodeGenerator = require('./generators/tapDataCodeGenerator');
const reportApiCallStats = require('./reportApiCallStats');
let timer;
configMonitor.on('message', (event) => {
  if (event && event.type === 'changed') {

    log.info('config is changed, regenerator api.')

    const config = event.data;

    // 生成代码
    generator(config).then().catch((err)=>{
      log.error('generation failed.',err)
    });
  }
});
configMonitor.start();

async function generator(config) {
  log.info('generating new apis code...');
  process.send('deploying');
  try {
    await new TapDataCodeGenerator(config).generate();
    log.info('code generation successful, restarting app server...');
    process.send('restart');
  } catch (e) {
    log.error('code generation failed.', e);
    process.send('deploy_fail');
  }
}

process.on('message', function(msg) {
  if (msg === 'ping') {
    if (timer) {
      try {
        clearTimeout(timer);
        timer = null;
      } catch (e) {
        log.error(e);
      }
    }
  }
});

setInterval(()=>{
  try {
    process.send('ping');
  }catch (e) {
    reportApiCallStats.stop(true);
  }
  timer = setTimeout(()=>{
    process.exit(0);
  },5000);
},5000)