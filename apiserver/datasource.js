/**
 * <AUTHOR>
 * @date 5/17/19
 * @description
 */
const log = require('./dist').log.app;
const axios = require('axios');
const path = require('path');
// const appConfig = require('./config');
const { getToken, removeToken } = require('./tapdata');
const checkEnableLoadSchemaFeature = require('./tapdata').checkEnableLoadSchemaFeature;
const MongoClient = require('mongodb').MongoClient;
const ConnectionString = require('mongodb-connection-string-url').default;
const Conf = require('conf');
const config = new Conf();
const { getCollectionSchema } = require('./load_schema_mongo');

const getConnection = function (token) {
	let url = config.get('tapDataServer.url') + '/api/Connections?access_token=' + token;
	try {
		let params = {
			'filter[where][status]': 'testing',
			'filter[where][database_type]': 'mongodb'
		};
		axios.get(url, {
			params: params,
			timeout: 5000,
			headers: {
				'Accept': 'application/json'
			}
		}).then(response => {
			const body = response.data;
			if(body){
				let parsedBody = body;
				if(typeof body === 'string') {
					try{
						parsedBody = JSON.parse(body);
					}catch (e) {
						log.error('parse config error: \n', e);
						return;
					}
				}
				if("ok" === parsedBody.code) {
					const data = parsedBody.data;
					if (data && data.length > 0) {
						data.forEach(v => {
							testConnection(v);
						})
					}
				} else {
					log.error('get connection error: \n', parsedBody.msg);
				}
			}
		}).catch(error => {
			if (error.response) {
				log.error('get connection error: \n', error.response.data);
			} else {
				log.error('get connection fail.', error.message);
			}
		});

	} catch (e) {
		log.error('get connection to test fail:\n', e);
	}
},

	testConnection = function (connection) {

		try {
			if (connection) {

				//const uri = "mongodb+srv://<username>:<password>@<your-cluster-url>/test?retryWrites=true";
				const validOptionNames = [
					'ssl',
					'sslValidate',
					'sslCA',
					'sslCert',
					'sslKey',
					'sslPass',
					'sslCRL',
					'checkServerIdentity'
				];
				const validOptions = {
					useNewUrlParser: true
				};
				let lbOptions = Object.keys(connection);
				lbOptions.forEach(function(option) {
					if (validOptionNames.indexOf(option) > -1) {
						if( connection[option]){
							validOptions[option] = connection[option];
						}
					}
				});
				if( validOptions.sslValidate) {
					validOptions.sslCA = Array.isArray(validOptions.sslCA) ? validOptions.sslCA :
						( validOptions.sslCA ? [validOptions.sslCA] : [])
				} else {
					delete validOptions.sslCA;
				}
				const uri = connection.database_uri;
				const validate_details = [];
				let uriObj;
				let parseError = null;

				try {
					uriObj = new ConnectionString(uri);
				} catch (err) {
					parseError = err;
				}

				validate_details.push({
					"stage_code": "validate-3000",
					"show_msg": "Checking the connection uri is available.",
					"status": parseError ? "invalid" : "passed",
					"sort": 1,
					"error_code": null,
					"fail_message": parseError ? parseError.toString() : null,
					"required": true
				});

				if (parseError) {
					log.error('connect to mongodb error\n', parseError);
					updateConnection(connection.id, {
						status: 'invalid',
						response_body: {
							validate_details: validate_details
						}
					}, function (err, data) { });
				} else {
					validate_details.push({
						"stage_code": "validate-3100",
						"show_msg": "Checking the connection database name is available.",
						"status": uriObj.pathname && uriObj.pathname !== '/' ? "passed" : "invalid",
						"sort": 1,
						"error_code": null,
						"fail_message": uriObj.pathname && uriObj.pathname !== '/' ? null : "No database name",
						"required": true
					});

					if (!uriObj.pathname || uriObj.pathname === '/') {
						updateConnection(connection.id, {
							status: 'invalid',
							response_body: {
								validate_details: validate_details
							}
						}, function (err, data) { });
					} else {
						const client = new MongoClient(uri, validOptions);
						client.connect(err => {

							validate_details.push({
								"stage_code": "validate-3100",
								"show_msg": "Checking the connection is available.",
								"status": err ? "invalid" : "passed",
								"sort": 1,
								"error_code": null,
								"fail_message": err ? err.toString() : null,
								"required": true
							});

							if (err) {
								log.error('connect to mongodb error\n', err);
								updateConnection(connection.id, {
									status: 'invalid',
									response_body: {
										validate_details: validate_details
									}
								}, function (err, data) {
									client.close();
								});
							} else {
								let databaseName = uriObj.pathname.substring(1) || 'test'; // Remove leading '/' from pathname
								log.info('connect mongodb, database name is ' + databaseName);
								databaseName = databaseName.split('/').join('_');
								const db = client.db(databaseName);

								db.collections(function (err, collections) {

									let pending = 0;
									const schema = {
										tables: []
									},
										errors = [],
										finish = function () {
											pending--;
											if (pending === 0) {

												validate_details.push({
													"stage_code": "validate-3200",
													"show_msg": "Trying to load schema.",
													"status": errors.length === 0 ? "passed" : "invalid",
													"sort": 2,
													"error_code": null,
													"fail_message": errors.length > 0 ? errors.join("\n") : null,
													"required": true
												});

												updateConnection(connection.id, {
													status: 'ready',
													schema: schema,
													response_body: {
														validate_details: validate_details
													}
												}, function (err, result) {
													if (err) {
														log.error('test mongodb connection fail\n', err);
													} else {
														log.info('test mongodb connection done.');
													}
													client.close();
												});
											}
										};
									collections.forEach((collection) => {
										pending++;
										getCollectionSchema(collection, function (err, collectionSchema) {
											if (err) {
												log.error('get collection schema fail\n', err);
												errors.push(err);
											} else {
												schema.tables.push(collectionSchema);
											}
											finish();
										});

									});

								});
							}
						});
					}
				}
			}
		} catch (e) {
			log.error('test connection fail\n', e);
		}

	},

	updateConnection = function (id, data, cb) {

		log.info('update connection: ' + id);
		getToken(function (token) {

			let url = config.get('tapDataServer.url') + `/api/Connections/${id}?access_token=` + token;
			try {
				axios.patch(url, data, {
					headers: {
						'Content-Type': 'application/json'
					},
					timeout: 5000
				}).then(response => {
					log.info('update connection success ' + id);
					cb(null, response.data);
				}).catch(error => {
					if (error.response) {
						if (error.response.status === 401 || error.response.status === 403) {
							log.error('Access token Expired');
							removeToken();
							cb({
								statusCode: error.response.status,
								msg: 'Access token expired'
							}, null);
						} else {
							log.error('update connection error: \n', error.response.data);
							cb(error.response.data, null);
						}
					} else {
						log.error('update connection fail.', error.message);
						cb(error, null);
					}
				});

			} catch (e) {
				log.error('update connection to test fail:\n', e);
				cb(e, null);
			}

		});

	};

let __intervalId = null;
exports.start = function () {

	checkEnableLoadSchemaFeature(enable => {
		if (enable) {
			setInterval(() => {
				try {
					getToken(token => {
						if (token)
							getConnection(token);
					})
				} catch (e) {
					log.error('get connection to test fail:\n', e);
				}

			}, 2000);
		}
	});
};
exports.stop = function () {
	if (__intervalId)
		clearInterval(__intervalId);
};
