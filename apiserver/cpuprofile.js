const fs = require('fs');
const profiler = require('v8-profiler');

let profileData;
let startProfile = false;

process.on('SIGUSR1', () => {
    if (startProfile) {
        console.log('Dumping data...');
        profileData = profiler.stopProfiling();
        console.log(profileData.getHeader());
        return profileData.export((err, result) => {
            if (err) {
                console.log(err);
            } else {
                fs.writeFileSync('profileData.cpuprofile', result)
                console.log('Dumping data done');
            }
            profileData.delete();
            profileData = null;
            startProfile = false;
        });
    }
    console.log('Start profile...');
    profiler.startProfiling('p1');
    startProfile = true;
});

