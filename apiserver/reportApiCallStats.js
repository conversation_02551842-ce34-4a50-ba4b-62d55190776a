const tapdata = require('./tapdata');
const axios = require('axios');
const Conf = require('conf');
const fs = require('fs');
const path = require('path');
const {randomUUID} = require('node:crypto');
const config = new Conf();
const log = require('./dist').log.app;

let apiCellCache = [];
let enableReportApiCall = config.get("apiStatsBatchReport.enableApiStatsBatchReport") === 'true' ||
	config.get("apiStatsBatchReport.enableApiStatsBatchReport") === true;
let sizeOfTriggerApiStatsBatchReport =
	Number(config.get("apiStatsBatchReport.sizeOfTriggerApiStatsBatchReport")) || 100;
let timeSpanOfTriggerApiStatsBatchReport =
	Number(config.get('apiStatsBatchReport.timeSpanOfTriggerApiStatsBatchReport')) || 60000;
let intervalId;

const directoryPath = config.get('apiCallHistoryCacheDir') || "./api_call_data";
log.info('apiCallHistoryCacheDir:' + directoryPath);
if (!fs.existsSync(directoryPath)) {
	fs.mkdir(directoryPath, {recursive: true}, (err) => {
		if (err) {
			log.error('create directory failed:', err.message);
		}
	});
}

const start = exports.start = function(){
	if( intervalId )
		clearInterval(intervalId);

	intervalId = setInterval(() => {
		reportApiCallStats();
	}, timeSpanOfTriggerApiStatsBatchReport);
};
const stop = exports.stop = function(){
	clearInterval(intervalId);
	reportApiCallStats(true);
};

exports.put = function(apiCell){
	if( enableReportApiCall ){
		apiCellCache.push(apiCell);
		if( apiCellCache.length >= sizeOfTriggerApiStatsBatchReport ){
			reportApiCallStats();
		}
	}
};

tapdata.on('apiStatsBatchReport.sizeOfTriggerApiStatsBatchReport:changed', (newVal, oldValue) => {
	sizeOfTriggerApiStatsBatchReport = Number(newVal) || 100;
});
tapdata.on('apiStatsBatchReport.timeSpanOfTriggerApiStatsBatchReport:changed', (newVal, oldValue) => {
	timeSpanOfTriggerApiStatsBatchReport = Number(newVal) || 60000;
	start();
});
tapdata.on('apiStatsBatchReport.enableApiStatsBatchReport:changed', (newVal, oldValue) => {
	if( newVal === 'true' || newVal === true) {
		enableReportApiCall = true;
		start();
	} else {
		enableReportApiCall = false;
		stop();
	}
});

function reportApiCallStats(isKill) {
	let apiAuditLogs = apiCellCache;
	apiCellCache = [];
	if (apiAuditLogs.length > 0) {
		const batchId =  randomUUID().toString();
		apiAuditLogs.forEach((apiCall) => {
			apiCall.batchId = apiCall.batchId || batchId;
		});
		try {
			tapdata.getToken(function(token) {
				if (!token) {
					saveAsync(apiAuditLogs);
					return
				}
				upload(token, apiAuditLogs, isKill);
				if (!isKill) {
					readAsync((data, filePath) => {
						upload(token, data, isKill, filePath);
					})
				}
			});
		} catch (e) {
			saveAsync(apiAuditLogs);
		}
	} else {
		if (!isKill) {
			tapdata.getToken(function(token) {
				readAsync((data, filePath) => {
					log.info('readAsync once');
					upload(token, data, isKill, filePath);
					log.info('readAsync complete');
				})
			});
		}
	}
}

function upload(token, apiAuditLogs, isKill, filePath) {
	let url = config.get("tapDataServer.url") + '/api/ApiCalls?access_token=' + token;
	for (let index = 0; index < apiAuditLogs.length; index++) {
		let from = index * 100;
		if (from > apiAuditLogs.length - 1) {
			break;
		}
		let to = from + 100;
		if (to > apiAuditLogs.length) {
			to = apiAuditLogs.length;
		}
		let sendData = apiAuditLogs.slice(from, to);
		const nowDate = new Date().getTime();
		sendData.forEach((apiCall) => {
			apiCall.report_time = nowDate
		});
		axios.post(url, sendData, {
			headers: {
				'Content-Type': 'application/json'
			},
			timeout: 5000
		}).then(response => {
			if (filePath) {
				fs.unlink(filePath, (deleteErr) => {
					if (deleteErr) {
						log.error('delete file failed:', deleteErr.message);
					}
				});
			}
			if (isKill) {
				process.exit(0)
			}
		}).catch(error => {
			saveAsync(sendData);
			if (error.response) {
				if (error.response.status === 401 || error.response.status === 403) {
					log.error('Access token Expired');
					tapdata.removeToken();
				} else {
					log.error('Failed to report api call stats:', JSON.stringify(error));
				}
			} else {
				log.error('Not response, failed to report api call stats: ', JSON.stringify(error));
			}
			if (isKill) {
				process.exit(0)
			}
		});
	}
}

async function saveAsync(data) {
	if (!data || data.length === 0) {
		return
	}
	if (!fs.existsSync(directoryPath)) {
		log.error("unable to find directory: " + directoryPath)
		return
	}
	const batchId = data[0].batchId;
	const filePath = path.join(directoryPath, batchId + '.json');
	fs.writeFile(filePath, JSON.stringify(data), 'utf8', (writeErr) => {
		if (writeErr) {
			log.error('failed to write file:', writeErr);
		}
	});
}

async function readAsync(accept) {
	if (!fs.existsSync(directoryPath)) {
		log.error("unable to find directory: " + directoryPath)
		return
	}
	fs.readdir(directoryPath, (err, items) => {
		if (err) {
			log.error('read directory failed:', err.message);
			return;
		}
		if (!items || items.length === 0) {
			log.info('no file to read');
			return
		}
		for (const file of items) {
			const filePath = path.join(directoryPath, file);
			if (!filePath.endsWith('.json')) {
				continue
			}
			fs.readFile(filePath, 'utf8', (err, fileContent) => {
				try {
					const jsonData = JSON.parse(fileContent);
					accept(jsonData, filePath);
				} catch (readErr) {
					log.error(`❌ read file ${file} failed:`, readErr.message);
				}
			});
		}
	});
}
