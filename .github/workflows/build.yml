name: OpenSource Version Construction

on:
  workflow_dispatch:
    inputs:
      frontend_branch:
        description: 'Tapdata-Enterprise-Web Repo Branch:'
        required: true
        type: string
        default: 'develop'
      connectors_branch:
        description: 'Tapdata-Connectors Repo Branch:'
        required: true
        type: string
        default: 'main'

env:
  REGISTRY: ghcr.io

jobs:

  Set-Branch:
    runs-on: ubuntu-latest
    timeout-minutes: 60
    outputs:
      TAPDATA_BRANCH: ${{ steps.set-outputs.outputs.TAPDATA_BRANCH }}
      TAG_NAME: ${{ steps.set-outputs.outputs.TAG_NAME }}
    steps:
      - name: Set Env
        run: |
          echo "TAPDATA_BRANCH=${{ github.ref_name }}" >> $GITHUB_ENV
      - name: Checkout Tapdata Code
        uses: actions/checkout@v3
        with:
          repository: 'tapdata/tapdata'
          ref: ${{ github.ref_name }}
          token: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
          path: tapdata
          fetch-depth: 0
      - name: Set Tag
        run: |
          cd tapdata
          main_tag=$(git branch --show-current | cut -d '-' -f 2)
          current_timestamp=$(date +%s)
          hex_timestamp=$(printf "%X" "$current_timestamp" | tr 'A-F' 'a-f')
          tag_name="$main_tag-$hex_timestamp"
          echo "TAG_NAME=$tag_name" >> $GITHUB_ENV
      - name: Set Outpus
        id: set-outputs
        run: |
          echo "::set-output name=TAPDATA_BRANCH::${TAPDATA_BRANCH}"
          echo "::set-output name=TAG_NAME::${TAG_NAME}"

  Sync-Code-to-Internal-Repo:
    uses: ./.github/workflows/sync-code.yml
    needs: Set-Branch
    with:
      tapdata: ${{ needs.Set-Branch.outputs.TAPDATA_BRANCH }}
      tapdata-connectors: ${{ inputs.connectors_branch }}
      tapdata-enterprise-web: ${{ inputs.frontend_branch }}
      tapdata-application: 'main'
      docs: 'community-data-sources-docs'
    secrets:
      TAPDATA_ENT_CICD_TOKEN: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
      GITEE_SSH_KEY: ${{ secrets.GITEE_SSH_KEY }}
      GITEE_KNOWN_HOSTS: ${{ secrets.GITEE_KNOWN_HOSTS }}

  Build-Tapdata:
    runs-on: office-scan
    timeout-minutes: 60
    needs:
      - Set-Branch
      - Sync-Code-to-Internal-Repo
    steps:
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Patch Maven Dependens
        run: |
          echo "${{ secrets.RSYNC_PASSWORD }}" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync --delete --password-file=/tmp/rsync.passwd -avz rsync://root@*************:873/data/enterprise-temp/tapdata/ /root/.m2/
      - name: Checkout Tapdata Repo
        run: |
          rm -rf tapdata
          git clone -b ${{ needs.Set-Branch.outputs.TAPDATA_BRANCH }} --single-branch *************:tapdata_1/tapdata.git
          cd tapdata && git fetch --tags
      - name: Get Tapdata Version
        run: |
          cd tapdata && tapdata_version=$(git rev-parse --short HEAD) && cd ..
          mkdir -p version/${{ needs.Set-Branch.outputs.TAG_NAME }}/
          echo "- tapdata: ${{ needs.Set-Branch.outputs.TAPDATA_BRANCH }} $tapdata_version" > version/${{ needs.Set-Branch.outputs.TAG_NAME }}/tapdata.version
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync -r --password-file=/tmp/rsync.passwd version/* rsync://root@*************:873/data/version/
      - name: compile opensource tapdata
        run: |
          cd tapdata 
          update-alternatives --set java /usr/java/jdk-17.0.12/bin/java
          export JAVA_HOME=/usr/java/jdk-17.0.12
          bash build/build.sh -c tapdata -u false -l "-Dmaven.compile.fork=true -P idaas -P oss"
          update-alternatives --set java /usr/java/jdk1.8.0_361/bin/java
          export JAVA_HOME=/usr/java/jdk1.8.0_361
      - name: Upload Outputs
        run: |
          cd tapdata && bash build/build.sh -p tapdata
          temp_dir=temp/${{ needs.Set-Branch.outputs.TAG_NAME }}
          mkdir -p $temp_dir
          cp -r output/* $temp_dir/
          echo "${{ secrets.RSYNC_PASSWORD }}" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync -r --password-file=/tmp/rsync.passwd temp/ rsync://root@*************:873/data/temp/

  Build-Connectors:
    runs-on: office-scan
    timeout-minutes: 60
    needs:
      - Set-Branch
      - Sync-Code-to-Internal-Repo
    steps:
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Tapdata Repo
        run: |
          rm -rf tapdata
          git clone -b ${{ needs.Set-Branch.outputs.TAPDATA_BRANCH }} --single-branch *************:tapdata_1/tapdata.git
          cd tapdata && git fetch --tags
      - name: Patch Maven Dependens
        run: |
          echo "${{ secrets.RSYNC_PASSWORD }}" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync --delete --password-file=/tmp/rsync.passwd -avz rsync://root@*************:873/data/enterprise-temp/tapdata/ /root/.m2/
      - name: Checkout Connectors Code
        run: |
          rm -rf tapdata-connectors
          git clone -b ${{ inputs.connectors_branch }} --single-branch *************:tapdata_1/tapdata-connectors.git
          cd tapdata-connectors && git fetch --tags
      - name: Get Connectors Version
        run: |
          cd tapdata-connectors && tapdata_connectors_version=$(git rev-parse --short HEAD) && cd ..
          mkdir -p version/${{ needs.Set-Branch.outputs.TAG_NAME }}/
          echo "- tapdata-connectors: ${{ inputs.connectors_branch }} $tapdata_connectors_version" > version/${{ needs.Set-Branch.outputs.TAG_NAME }}/tapdata-connectors.version
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync -r --password-file=/tmp/rsync.passwd version/* rsync://root@*************:873/data/version/
      - name: compile tapdata connectors
        run: |
          cd tapdata
          update-alternatives --set java /usr/java/jdk-17.0.12/bin/java
          export JAVA_HOME=/usr/java/jdk-17.0.12
          bash build/build.sh -c connectors -u false -l "-Dmaven.compile.fork=true"
          update-alternatives --set java /usr/java/jdk1.8.0_361/bin/java
          export JAVA_HOME=/usr/java/jdk1.8.0_361
      - name: Upload Outputs
        run: |
          cd tapdata && bash build/build.sh -p connectors
          temp_dir=temp/${{ needs.Set-Branch.outputs.TAG_NAME }}
          mkdir -p $temp_dir
          cp -r output/* $temp_dir/
          echo "${{ secrets.RSYNC_PASSWORD }}" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync -r --password-file=/tmp/rsync.passwd temp/ rsync://root@*************:873/data/temp/

  Build-Frontend:
    runs-on: office-scan
    timeout-minutes: 60
    needs:
      - Set-Branch
      - Sync-Code-to-Internal-Repo
    steps:
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Tapdata Repo
        run: |
          rm -rf tapdata
          git clone -b ${{ needs.Set-Branch.outputs.TAPDATA_BRANCH }} --single-branch *************:tapdata_1/tapdata.git
          cd tapdata && git fetch --tags
      - name: Checkout Docs Repo
        run: |
          rm -rf docs
          git clone -b community-data-sources-docs --single-branch *************:tapdata_1/docs.git
          cd docs && git fetch --tags
      - name: Checkout Frontend Code
        run: |
          rm -rf tapdata-enterprise-web tapdata-web
          git clone -b ${{ inputs.frontend_branch }} --single-branch *************:tapdata_1/tapdata-web.git
          cd tapdata-web && git fetch --tags && cd ..
          mkdir -p tapdata-enterprise-web
          rsync -av tapdata-web/ tapdata-enterprise-web/
      - name: Get Frontend Version
        run: |
          cd tapdata-enterprise-web && tapdata_enterprise_web_version=$(git rev-parse --short HEAD) && cd ..
          mkdir -p version/${{ needs.Set-Branch.outputs.TAG_NAME }}/
          echo "- tapdata-enterprise-web: ${{ inputs.frontend_branch }} $tapdata_enterprise_web_version" > version/${{ needs.Set-Branch.outputs.TAG_NAME }}/tapdata-enterprise-web.version
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync -r --password-file=/tmp/rsync.passwd version/* rsync://root@*************:873/data/version/
      - name: Build Frontend
        run: |
          export PATH="/root/.nvm/versions/node/v20.12.2/bin:$PATH"
          pnpm config set registry https://registry.npmmirror.com
          cd tapdata && bash build/build.sh -c frontend -t ${{ needs.Set-Branch.outputs.TAG_NAME }}
      - name: Build Docs
        run: |
          cd docs && npm install && npm run build:docs && cd ..
          mkdir -p tapdata-enterprise-web/dist/docs/
          rsync -av docs/build/ tapdata-enterprise-web/dist/docs/
      - name: Upload Outputs
        run: |
          cd tapdata && bash build/build.sh -p frontend
          temp_dir=temp/${{ needs.Set-Branch.outputs.TAG_NAME }}
          mkdir -p $temp_dir
          cp -r output/* $temp_dir/
          echo "${{ secrets.RSYNC_PASSWORD }}" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync -r --password-file=/tmp/rsync.passwd temp/ rsync://root@*************:873/data/temp/

  Build-Image:
    runs-on: office-scan
    timeout-minutes: 60
    needs:
      - Build-Tapdata
      - Build-Connectors
      - Build-Frontend
      - Set-Branch
    steps:
      - name: Clean Directory
        run: |
          rm -rf ./*
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Tapdata Repo
        run: |
          rm -rf tapdata
          git clone -b ${{ needs.Set-Branch.outputs.TAPDATA_BRANCH }} --single-branch *************:tapdata_1/tapdata.git
          cd tapdata && git fetch --tags
      - name: Package and Push Image
        run: |
          cd tapdata
          echo "${{ secrets.RSYNC_PASSWORD }}" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          mkdir -p output
          rsync -r --links --progress --password-file=/tmp/rsync.passwd rsync://root@*************:873/data/temp/${{ needs.Set-Branch.outputs.TAG_NAME }}/ output/
          echo '${{ secrets.HARBOR_PASS }}' | docker login harbor.internal.tapdata.io --username=${{ secrets.HARBOR_USER }} --password-stdin
          bash build/build.sh -o docker -t ${{ needs.Set-Branch.outputs.TAG_NAME }}
      - name: Print Image Info
        run: |
          echo '**Google Docker Image:**' >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
          echo "harbor.internal.tapdata.io/tapdata/tapdata:${{ needs.Set-Branch.outputs.TAG_NAME }}" >> $GITHUB_STEP_SUMMARY
          echo '```' >> $GITHUB_STEP_SUMMARY
      - name: Checkout Tapdata Application
        run: |
          rm -rf tapdata-application
          git clone -b version --single-branch *************:tapdata_1/tapdata-application.git
      - name: Add Commit ID and Version Map
        run: |
          echo "${{ secrets.RSYNC_PASSWORD }}" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync -r --progress --password-file=/tmp/rsync.passwd rsync://root@*************:873/data/version/${{ needs.Set-Branch.outputs.TAG_NAME }}/ ./
          echo "## ${{ needs.Set-Branch.outputs.TAG_NAME }}" >> tapdata-application/README.md
          cat tapdata.version >> tapdata-application/README.md
          cat tapdata-enterprise-web.version >> tapdata-application/README.md
          cat tapdata-connectors.version >> tapdata-application/README.md
      - uses: stefanzweifel/git-auto-commit-action@v4
        with:
          commit_message: "Update README.md"
          branch: 'version'
          repository: 'tapdata-application'
          commit_user_name: 'cicd'
          commit_user_email: '<EMAIL>'
  
  Make-Tar-File:
    runs-on: office-scan
    timeout-minutes: 60
    needs:
      - Build-Tapdata
      - Build-Connectors
      - Build-Frontend
      - Set-Branch
    steps:
      - name: Clean Directory
        run: |
          rm -rf ./*
      - name: Configure SSH
        run: |
          mkdir -p ~/.ssh
          echo "Host gitee.com" > ~/.ssh/config
          echo "  StrictHostKeyChecking no" >> ~/.ssh/config
          chmod 600 ~/.ssh/config
      - name: Checkout Tapdata Repo
        run: |
          rm -rf tapdata
          git clone -b ${{ needs.Set-Branch.outputs.TAPDATA_BRANCH }} --single-branch *************:tapdata_1/tapdata.git
          cd tapdata && git fetch --tags
      - name: Package and Push Tar File
        run: |
          cd tapdata
          echo "${{ secrets.RSYNC_PASSWORD }}" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          mkdir -p output
          rsync -r --links --progress --password-file=/tmp/rsync.passwd rsync://root@*************:873/data/temp/${{ needs.Set-Branch.outputs.TAG_NAME }}/ output/
          bash build/build.sh -o tar -t ${{ needs.Set-Branch.outputs.TAG_NAME }} -m x86_64
          rsync -r --progress --password-file=/tmp/rsync.passwd output/tapdata-x86_64-${{ needs.Set-Branch.outputs.TAG_NAME }}.tar.gz rsync://root@*************:873/data/enterprise-artifact/gz/
          rm -rf output/tapdata-x86_64-${{ needs.Set-Branch.outputs.TAG_NAME }}.tar.gz
          bash build/build.sh -o tar -t ${{ needs.Set-Branch.outputs.TAG_NAME }} -m arm64
          rsync -r --progress --password-file=/tmp/rsync.passwd output/tapdata-arm64-${{ needs.Set-Branch.outputs.TAG_NAME }}.tar.gz rsync://root@*************:873/data/enterprise-artifact/gz/

  Clean-Build-Temp:
    runs-on: office-scan
    timeout-minutes: 30
    needs:
      - Make-Tar-File
      - Build-Image
      - Set-Branch
    if: ${{ always() }}
    steps:
      - name: Clean Build Temp
        run: |
          mkdir -p temp/${{ needs.Set-Branch.outputs.TAG_NAME }}/
          echo "${{ secrets.RSYNC_PASSWORD }}" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync -r --delete --password-file=/tmp/rsync.passwd temp/${{ needs.Set-Branch.outputs.TAG_NAME }}/ rsync://root@*************:873/data/temp/${{ needs.Set-Branch.outputs.TAG_NAME }}/
