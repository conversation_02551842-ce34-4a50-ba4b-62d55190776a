name: LDP Continuous Integration With TEST

on:
  push:
    branches: [ "master", "release-v*.*.*", "develop-v*.*.0", "develop-v*.*", "release-v*.*" ]
  pull_request:
    branches: [ "master", "release-v*.*.*", "develop-v*.*.0", "develop-v*.*", "release-v*.*" ]
  schedule:
    - cron: "30 17 * * *"
    - cron: "30 20 * * *"
    - cron: "30 1 * * *"

env:
  REGISTRY: ghcr.io
  TEST_DATABASE: ${{ secrets.TEST_DATABASE }}
  PYPI_USERNAME: ${{ secrets.PYPI_USERNAME }}
  PYPI_PASSWORD: ${{ secrets.PYPI_PASSWORD }}
  FEISHU_PERSON_IN_CHARGE: ${{ secrets.FEISHU_PERSON_IN_CHARGE }}
  FEISHU_CHAT_ID: ${{ secrets.FEISHU_CHAT_ID }}
  FEISHU_APP_ID: ${{ secrets.FEISHU_APP_ID}}
  FEISHU_APP_SECRET: ${{ secrets.FEISHU_APP_SECRET }}


jobs:

  Code-Style-Check:
    runs-on: ubuntu-latest
    outputs:
      check-style: ${{ steps.check-style.outputs.error }}
    steps:
      - name: Checkout Code to Get Message
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - name: Set env
        run: |
          message=`git log -1 --pretty=%B`
          echo "COMMIT_MESSAGE="$message"" >> $GITHUB_ENV
      - name: check commit message
        id: check-style
        if: (!startsWith(env.COMMIT_MESSAGE, 'feat')) && 
            (!startsWith(env.COMMIT_MESSAGE, 'fix')) &&
            (!startsWith(env.COMMIT_MESSAGE, 'refactor')) &&
            (!startsWith(env.COMMIT_MESSAGE, 'test')) &&
            (!startsWith(env.COMMIT_MESSAGE, 'chore')) &&
            (!startsWith(env.COMMIT_MESSAGE, 'Merge'))
        run: |
          echo ${{ env.COMMIT_MESSAGE }}
          echo "error=true" >>$GITHUB_OUTPUT

  Integration-Testing:
    runs-on: ubuntu-latest
    needs: Code-Style-Check
    timeout-minutes: 60
    if: needs.Code-Style-Check.outputs.check-style != 'true'
    steps:
      - name: Set Env if Push
        if: ${{ github.event_name == 'push' }}
        run: |
          echo "CURRENT_BRANCH=${{ github.ref_name }}" >> $GITHUB_ENV
          echo "TAG_BRANCH=${{ github.ref_name }}" >> $GITHUB_ENV
      - name: Set Env if Pull Request
        if: ${{ github.event_name == 'pull_request' }}
        run: |
          echo "CURRENT_BRANCH=${{ github.ref }}" >> $GITHUB_ENV
          echo "TAG_BRANCH=${{ github.base_ref }}" >> $GITHUB_ENV
          echo "server_url=${{ github.server_url }}" >> $GITHUB_ENV
          echo "repository=${{ github.repository }}" >> $GITHUB_ENV
          echo "run_id=${{ github.run_id }}" >> $GITHUB_ENV
          echo "FEISHU_APP_ID=${{ env.FEISHU_APP_ID }}" >> $GITHUB_ENV
          echo "FEISHU_PERSON_IN_CHARGE=${{ env.FEISHU_PERSON_IN_CHARGE }}" >> $GITHUB_ENV
          echo "GITHUB_TOKEN=${{ secrets.GITHUB_TOKEN }}" >> $GITHUB_ENV
          echo "FEISHU_APP_SECRET=${{ env.FEISHU_APP_SECRET }}" >> $GITHUB_ENV
      - name: Checkout Enterprise Repo to Set Env if Cron Job
        if: ${{ github.event_name == 'schedule' }}
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - name: Set Env if Cron Job
        if: ${{ github.event_name == 'schedule' }}
        run: |
          current_branch="`git branch -r | cut -d "/" -f 2 | grep '^develop-v[0-9]*.[0-9]*.[0-9]*$' | sort -rV | head -n 1`"
          echo "CURRENT_BRANCH=$current_branch" >> $GITHUB_ENV
          echo "TAG_BRANCH=$current_branch" >> $GITHUB_ENV
      - uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - name: Login to the github container registry
        uses: docker/login-action@f054a8b539a109f9f41c372932f1ae047eff08c9
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
      - name: get test history
        uses: actions/checkout@v2
        if: always()
        continue-on-error: true
        with:
          ref: report-test
          path: report-test
      - name: compile plugin-kit module
        run: |
          chmod u+x build/build.sh
          build/build.sh -c plugin-kit || exit 1
      - name: file-storages 模块编译
        run: |
          chmod u+x build/build.sh
          build/build.sh -c file-storages || exit 1
      - name: compile connectors-common module
        run:
          build/build.sh -c connectors-common
      - name: compile manager module
        run:
          build/build.sh -c manager
      - name: compile iengine modules
        run: |
          build/build.sh -c iengine
      - name: compile connectors module
        run:
          build/build.sh -c connectors
      - name: compile opensource tapdata-cli module
        run:
          build/build.sh -c tapdata-cli
      - name: package module and make image
        run: |
          cur_timestamp=$((`date '+%s'`))
          tag=`git describe --tag --long HEAD | awk -F '-' '{print $3}'`"-${cur_timestamp}"
          docker_tag="ghcr.io/tapdata/${{ env.TAG_BRANCH }}:$tag"
          echo $docker_tag > build/image/tag
          echo "docker_tag=$docker_tag" >> $GITHUB_ENV
          sudo build/build.sh -p 1 -o image
      - name: "启动本地测试环境"
        run: |
          echo $docker_tag
          docker run -e mode=use --name tapd -d -p 3000:3000 $docker_tag bash
          bash build/wait_tapd_ready.sh
      - name: "基本用例集成测试"
        env:
          CASE_CONFIG: ${{ secrets.CASE_CONFIG }}
        run: |
          sudo apt update -y
          sudo apt install -y libmysqlclient-dev
          pip3 install poetry
          cd tapshell && pip3 install . && cd ..
          cd auto-test && pip3 install -r requirements.txt && cd -
          cd auto-test && chmod +x ./test.sh && bash ./test.sh
          if [[ -f /tmp/fail ]]; then echo "**基本用例集成测试结果: 未通过, 请点开步骤检查详细日志**" >> $GITHUB_STEP_SUMMARY; fi
          if [[ ! -f /tmp/fail ]]; then echo "**基本用例集成测试结果: 通过**" >> $GITHUB_STEP_SUMMARY; fi
      - name: Setup Poetry
        uses: Gr1N/setup-poetry@v7
      - name: publish pip package if push
        if: ${{ success() && env.CURRENT_BRANCH == 'master' && github.event_name == 'push' }}
        run: |
          git diff-tree --name-only HEAD^ HEAD | grep tapshell > /dev/null
          if [[ $? -ne 0 ]]; then echo "Do not publish pip package" && exit 0; fi
          cd tapshell && poetry build && poetry publish --username ${{ env.PYPI_USERNAME }} --password ${{ env.PYPI_PASSWORD }}
      - name: update github page
        id: gh_pages
        if: always()
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_branch: report-test
          publish_dir: gh_pages
          keep_files: true
      - name: print summary
        if: ${{ steps.gh_pages.conclusion == 'success' }}
        env:
          RUN_SIGN: ${{ github.run_id }}-${{ github.run_number }}-${{ github.run_attempt }}
          BRANCH: ${{ github.ref }}
        run: |
          BRANCH_DIR=`echo $BRANCH | sed "s:/:-:g"`
          echo "[Click to view test report](https://tapdata.github.io/tapdata/$BRANCH_DIR/$RUN_SIGN)" >> $GITHUB_STEP_SUMMARY
