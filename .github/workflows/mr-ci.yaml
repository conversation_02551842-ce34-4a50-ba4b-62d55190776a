name: Merge Request CI and Nightly Build

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}

on:
  push:
    branches: [ "main" ]
  pull_request:
    branches: [ "main", "develop", "release-v*.*" ]
  schedule:
    - cron: "0 21 * * *"
  workflow_dispatch:
    inputs:
      tapce_run:
        description: 'Tapce run or not'
        required: true
        type: boolean
        default: false
      tapdata_branch:
        description: 'tapdata branch'
        required: true
        type: string
        default: 'develop'

jobs:

  Get-Stable-Branch:
    runs-on: ubuntu-latest
    outputs:
      TAPDATA_BRANCH: ${{ steps.set-output.outputs.TAPDATA_BRANCH }}
      ENTERPRISE_BRANCH: ${{ steps.set-output.outputs.ENTERPRISE_BRANCH }}
      FRONTEND_BRANCH: ${{ steps.set-output.outputs.FRONTEND_BRANCH }}
      TAPCE_BRANCH: ${{ steps.set-output.outputs.TAPCE_BRANCH}}
      TAG_NAME: ${{ steps.set-output.outputs.TAG_NAME }}
    steps:
      - name: Checkout Tapdata Opensource
        uses: actions/checkout@v3
        with:
          repository: 'tapdata/tapdata'
          token: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
          path: tapdata
          fetch-depth: 0
      - name: Set Tag
        run: |
          cd tapdata
          main_tag=$(git describe --tags | cut -d '-' -f 1)
          current_timestamp=$(date +%s)
          hex_timestamp=$(printf "%X" "$current_timestamp" | tr 'A-F' 'a-f')
          tag_name="$main_tag-$hex_timestamp"
          echo "TAG_NAME=$tag_name" >> $GITHUB_ENV
      - name: Checkout Tapdata Enterprise Web
        uses: actions/checkout@v2
        with:
          repository: 'tapdata/tapdata-enterprise-web'
          token: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
          path: tapdata-enterprise-web
          depth: 0
      - name: Checkout Tapdata Enterpris
        uses: actions/checkout@v2
        with:
          repository: 'tapdata/tapdata-enterprise'
          token: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
          path: tapdata-enterprise
          depth: 0
      - name: Checkout Tapdata Application
        uses: actions/checkout@v2
        with:
          repository: 'tapdata/tapdata-application'
          ref: "main"
          token: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
          path: tapdata-application
      - name: Checkout Tapce
        uses: actions/checkout@v2
        with:
          repository: 'tapdata/tapce'
          ref: "main"
          token: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
          path: tapce
      - name: Get Tapdata Branch Name -- Common
        run: |
          echo "TAPDATA_BRANCH=${{ github.ref_name }}" >> $GITHUB_ENV
      - name: Get Tapdata Branch Name -- Pr
        if: github.event_name == 'pull_request'
        run: |
          echo "TAPDATA_BRANCH=${{ github.event.pull_request.head.ref }}" >> $GITHUB_ENV
      - name: Get Tapdata Branch Name -- Workflow Dispatch
        if: github.event_name == 'workflow_dispatch'
        run: |
          echo "TAPDATA_BRANCH=${{ github.event.inputs.tapdata_branch }}" >> $GITHUB_ENV
      - name: Get Tapdata Branch Name -- Schedule
        if: github.event_name == 'schedule'
        run: |
          echo "TAPDATA_BRANCH=develop" >> $GITHUB_ENV
      - name: Get last stable branch
        id: set-output
        run: |
          cd tapdata-application
          TAPDATA_BRANCH=${{ env.TAPDATA_BRANCH }}
          ENTERPRISE_BRANCH="develop"
          FRONTEND_BRANCH="develop"
          TAPCE_BRANCH="develop"
          echo "::set-output name=TAPDATA_BRANCH::${TAPDATA_BRANCH}"
          echo "::set-output name=ENTERPRISE_BRANCH::${ENTERPRISE_BRANCH}"
          echo "::set-output name=FRONTEND_BRANCH::${FRONTEND_BRANCH}"
          echo "::set-output name=TAG_NAME::${TAG_NAME}"
          echo "::set-output name=TAPCE_BRANCH::${TAPCE_BRANCH}"

  Push-Code-To-Gitee:
    uses: ./.github/workflows/sync-code.yml
    needs: Get-Stable-Branch
    with:
      tapdata: ${{ needs.Get-Stable-Branch.outputs.TAPDATA_BRANCH }}
      tapdata-connectors: ''
      tapdata-enterprise-web: ''
      tapdata-application: 'main'
      docs: ''
    secrets:
      TAPDATA_ENT_CICD_TOKEN: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
      GITEE_SSH_KEY: ${{ secrets.GITEE_SSH_KEY }}
      GITEE_KNOWN_HOSTS: ${{ secrets.GITEE_KNOWN_HOSTS }}
  
  Scan-Tapdata:
    runs-on: office-scan
    if: ${{ !startsWith(github.base_ref, 'release-v') && !(github.event_name == 'schedule' || inputs.tapce_run) }}
    needs:
      - Get-Stable-Branch
      - Push-Code-To-Gitee
    timeout-minutes: 60
    steps:
      - name: Clean Directory
        run: |
          rm -rf ./*
      - name: Set SSH Key
        run: |
          echo '${{ secrets.GITEE_SSH_KEY }}' > /root/.ssh/id_rsa
          chmod 600 /root/.ssh/id_rsa
          echo '${{ secrets.GITEE_KNOWN_HOSTS }}' > /root/.ssh/known_hosts
      - name: Checkout Tapdata Code
        run: |
          git clone -b ${{ needs.Get-Stable-Branch.outputs.TAPDATA_BRANCH }} *************:${{ secrets.GITEE_USER }}/tapdata.git
          cd tapdata && git fetch --tags
      - name: Patch Maven Dependens
        run: |
          echo "Gotapd8!" > /tmp/rsync.passwd && chmod 600 /tmp/rsync.passwd
          rsync --delete --password-file=/tmp/rsync.passwd -avz rsync://root@*************:873/data/enterprise-temp/tapdata/ /root/.m2/
      - name: Build Tapdata - And Analyze
        env:
          GITHUB_TOKEN: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        run: |
          update-alternatives --set java /usr/java/jdk-17.0.12/bin/java
          export JAVA_HOME=/usr/java/jdk-17.0.12
          cd tapdata && mvn clean test -T1C -Dmaven.compile.fork=true -P idaas && mvn sonar:sonar \
            -Dsonar.projectKey=tapdata -Dsonar.host.url=${{ secrets.SONAR_HOST }} \
             -Dsonar.login=${{ secrets.SONAR_USER }} -Dsonar.password=${{ secrets.SONAR_PASS }} \
             -Dsonar.branch.name=${{ needs.Get-Stable-Branch.outputs.TAPDATA_BRANCH }}
          update-alternatives --set java /usr/java/jdk1.8.0_361/bin/java
          export JAVA_HOME=/usr/java/jdk1.8.0_361
      - name: Prevent Skip Unittest
        run: |
          cd tapdata
          find ./ -name jacoco | grep iengine
          find ./ -name jacoco | grep manager
      - name: Install Dependens
        run: |
          apt install -y jq
      - name: SonarQube Quality Gate check
        id: sonar
        uses: sonarsource/sonarqube-quality-gate-action@master
        env:
           SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
        with:
          scanMetadataReportFile: tapdata/target/sonar/report-task.txt
      - name: Send SonarQube Quality Gate to Pr Comment
        if: ${{ always() && steps.sonar.outcome == 'failure' }}
        run: |
          cd tapdata/build/ && bash check_sonarqube.sh --project-key=tapdata --branch=${{ needs.Get-Stable-Branch.outputs.TAPDATA_BRANCH }} \
             --sonar-token=${{ secrets.SONAR_TOKEN }} --github-token=${{ secrets.TAPDATA_ENT_CICD_TOKEN }} \
             --repo=tapdata --pr-number=${{ github.event.pull_request.number }}

  Build-Deploy-Test:
    runs-on: ubuntu-latest
    if: ${{ github.event_name == 'schedule' || inputs.tapce_run }}
    needs:
      - Push-Code-To-Gitee
      - Get-Stable-Branch
    outputs:
      IP: ${{ steps.get_ip_port.outputs.IP }}
      PORT: ${{ steps.get_ip_port.outputs.PORT }}
    steps:
      - name: Trigger - Build - Deploy - Test
        uses: convictional/trigger-workflow-and-wait@v1.6.1
        with:
          owner: tapdata
          repo: tapdata-application
          github_token: ${{ secrets.TAPDATA_ENT_CICD_TOKEN }}
          workflow_file_name: build-tapdata-op.yaml
          ref: main
          wait_interval: 60
          client_payload: '{"BUILD_CONFIG": "{\"FRONTEND_BRANCH\": \"${{ needs.Get-Stable-Branch.outputs.FRONTEND_BRANCH }}\", \"OPENSOURCE_BRANCH\": \"${{ needs.Get-Stable-Branch.outputs.TAPDATA_BRANCH }}\", \"ENTERPRISE_BRANCH\": \"${{ needs.Get-Stable-Branch.outputs.ENTERPRISE_BRANCH }}\", \"CONNECTORS_BRANCH\": \"develop#develop\", \"TAG_NAME\": \"${{ needs.Get-Stable-Branch.outputs.TAG_NAME }}\"}", "CONNECTORS-OPTIONS": "nightly-build", "JAVA_VERSION": "java17", "RUN_TEST": true}'
          propagate_failure: false
          trigger_workflow: true
          wait_workflow: true
