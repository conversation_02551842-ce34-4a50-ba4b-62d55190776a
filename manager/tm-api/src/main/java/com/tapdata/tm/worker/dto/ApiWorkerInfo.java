package com.tapdata.tm.worker.dto;

import lombok.Data;

import java.util.Map;

/**
 * <AUTHOR> href="<EMAIL>"><PERSON><PERSON></a>
 * <AUTHOR> href="https://github.com/11000100111010101100111"><PERSON></a>
 * @version v1.0 2025/9/1 10:02 Create
 * @description
 */
@Data
public class ApiWorkerInfo {
    Integer id;
    Integer pid;
    String oid;
    String name;
    String description;
    String workerStatus;
    Long workerStartTime;
    Map<String, Object> metricValues;
    Integer sort;

    Long createdTime;
    Long updatedTime;
}
