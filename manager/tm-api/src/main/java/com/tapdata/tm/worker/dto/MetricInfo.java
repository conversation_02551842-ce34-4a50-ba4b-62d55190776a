package com.tapdata.tm.worker.dto;

import lombok.Data;

/**
 * <AUTHOR> href="<EMAIL>"><PERSON><PERSON></a>
 * <AUTHOR> href="https://github.com/11000100111010101100111"><PERSON><PERSON></a>
 * @version v1.0 2025/9/4 15:11 Create
 * @description
 */
@Data
public class MetricInfo {
    /**
     * unit byte
     * */
    Long heapMemoryUsage;

    /**
     * unit %, such as 0.1 means 10%
     * */
    Double cpuUsage;

    /**
     * timestamp, unit ms
     * */
    Object lastUpdateTime;
}
