package com.tapdata.tm.apiServer.vo.metric;

import lombok.Data;

/**
 * <AUTHOR> href="<EMAIL>"><PERSON></a>
 * <AUTHOR> href="https://github.com/11000100111010101100111"><PERSON></a>
 * @version v1.0 2025/9/3 14:13 Create
 * @description
 */
@Data
public class MetricDataBase {

    /**
     * timestamp (ms)
     * */
    Long time;

    public MetricDataBase time(Long time) {
        this.time = time;
        return this;
    }

    public Object[] values() {
        return new Object[] {};
    }
}
