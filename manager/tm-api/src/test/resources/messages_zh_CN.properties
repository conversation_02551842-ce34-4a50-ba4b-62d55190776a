user.name=系統处理失败

NotLogin=用户没有登录
NotAuthorized=用户没有权限

IllegalArgument=无效参数: {0}

#??
connection.create=创建连接
connection.reset=重置连接
connection.copy=复制连接
desc.connection.create=创建了连接
desc.connection.reset=重置了连接
desc.connection.copy=复制了连接


#??
dataflows.create=创建任务
dataflows.reset=重置任务
dataflows.copy=拷贝任务
desc.dataflows.create=创建了任务
desc.dataflows.reset=重置了任务
desc.dataflows.copy=复制了任务


Inspect.Name.Exist=名称已存在，请修改
Inspect.ProcessId.NotFound= 找不到对应的agent
Inspect.agentTag.null=tag不能为空
Inspect.task.null=task不能为空
Inspect.Recovery.NotFieldMethod=非字段校验任务
Inspect.Recovery.StatusNotDone=校验任务不是已完成状态
Inspect.Recovery.ResultNotFound=校验任务没有差异结果
Inspect.Recovery.IsNotWithTask=校验不是基于同步任务创建
Inspect.Recovery.TaskNotFound=同步任务不存在
Inspect.Recovery.TaskTypeIsNotCDC=同步任务不包含增量
Inspect.Recovery.TaskIsNotRunning=同步任务非运行状态
Inspect.Recovery.TaskSyncStatusIsNotCDC=同步任务不在增量阶段

Inspect.ExportEventSql.TargetConnectionNotSupport=目标数据源暂不支持导出修复SQL
Inspect.ExportEventSql.fail=导出修复SQL失败
Inspect.ExportEventSql.TargetConnectionNotFound=目标连接不存在
Inspect.ExportEventSql.Exporting=修复文件正在导出中
Inspect.ExportEventSql.Exported=修复文件已导出



Function.Name.Exist=名称已存在，请修改

Task.statusIsNotStop=任务状态不是停止状态不允许操作
Task.StartStatusInvalid=任务启动状态异常
Task.DeleteStatusInvalid=任务删除状态异常
Task.NotFound=任务不存在
Task.OldVersion=当前编辑的任务不是最新的版本
Task.HotUpdateFailed=任务当前状态不允许当前修改
Task.DeleteSubTask=删除之后将无法恢复
Task.UpdateSubTask=更新之后，需要重置子任务才能生效
Task.PauseStatusInvalid=任务停止状态异常
DAG.NotNodes=节点为空
DAG.NotEdges=连线为空
DAG.NodeTooFew=节点少于两个
DAG.NodeIsolated=节点不能孤立
DAG.EdgeNotLink=连线两边没有节点
DAG.IsCyclic=节点连成环
DAG.SourceIsNotData=源节点必须是数据节点
DAG.TailIsNotData=尾结点必须是数据节点
DAG.MigrateTaskNotContainsTable=复制任务没有没有选择的表
DAG.JonNode=Join节点需要配置两个前置节点
Task.DeleteSubTaskIsRun=子任务正在运行中，不能被删除
Task.ListWarnMessage=节点配置错误
Task.RepeatName=任务名称重复
Task.NameIsNull=任务名称为空
Task.AgentNotFound=没有找到可用的agent实例，调度失败
Task.AgentConflict=所属agent节点冲突
Task.ResetTimeout=任务重置超时
Task.DeleteTimeout=任务删除超时
Task.StartCheckModelFailed=任务启动检查模型失败，请点击保存后再试。
Task.ImportFormatError=任务导入格式有误
Task.nodeRefresh=任务节点已经被刷新
Task.DateProcessConfigInvalid=时间处理节点选择时间类型异常
Task.ScheduleLimit=引擎可以被调用的任务超过了限制数，请升级订阅以获取更多任务数量
Task.ManuallyScheduleLimit=用户指定引擎可以被调用的任务超过了限制数，请替换其他引擎 {0}
Task.LicenseScheduleLimit=可以调度的通道数超过了配额，请更新license配额或停止相关任务
License.NodeInstanceIdInvalid=请确保节点 [{0}] 为数据源节点
Task.ResetAgentNotFound=重置任务没有找到可用的引擎，请启动/重启引擎后再试
Task.ResetStatusInvalid=当前需要重置的任务状态被更新，请刷新页面再试
Task.NotfoundDatasource=找不到数据源 '{0}'
Task.HeartbeatStartError=心跳任务异常，请关闭源节点连接（{0}）的心跳选项或尝试重启动当前任务
LogCollect.ConfigUpdateError=当前条件不允许修改
LogCollect.ExternalStorageUpdateError=当前条件不允许修改外存配置
LogCollect.UriInvalid=Mongodb uri 填写不合法
Tag.RepeatName=标签的名称已经存在
Tag.NotFound=找不到标签
Tag.TagExist=标签已经存在
DataRules.RepeatName=数据规则名称已经存在
DataRules.ModulesNotFound=模块没有找到
DatasourceDefine.NotFound=数据源定义没有找到
Datasource.RepeatName=数据源名称已存在
Datasource.NotFound=数据源不存在
Datasource.IsInit=系统数据源不能删除
Datasource.AgentNotFound=agent不存在
Datasource.LinkJobs=存在关联的任务，不允许当前操作
Datasource.DuplicateSource=数据源重复
Datasource.TableNotFound=表不存在
Datasource.SchemaNotFound=Schema不存在
Datasource.IllegalName=名称不合法，不能以数字和特殊字符开头
Datasource.IllegalUserNameOrPasswd=mongodb的uri模式用户名或者密码中不能包含特殊字符（:,@,%）
MetaData.HistoryVersionInvalid=模型历史版本有误
MetaData.HistoryNotFound=模型版本历史不存在
Datasource.MongoUriIsBlank=uri为空

# DataFlow
DataFlow.Not.Found=任务不存在
Not.Supported=暂不支持
Name.Already.Exists=名称已存在
Transition.Not.Supported=当前状态不支持此事件
Transition.Not.Found=找不到可转换的配置

Agent.Not.Found=没有可用的Agent
UserId.Not.Found=用户id不存在
User.Not.Found=用户不存在
User.Already.Exists=用户已存在
Role.Already.Exists=角色已存在
Incorrect.Password=密码不正确
Incorrect.Vague=邮箱或密码错误，请检查后重新输入

Resource.Not.Found=资源不存在

UserGroup.Exists.User=无法删除,当前用户组有用户

Too.Many.Login.Failures=密码错误达到最大次数，请于10分钟后再登录
Illegal.license=无效License
Sid.is.wrong=sid错误


Modules.BasePathAndVersion.Existed=API请求路径由（版本，前缀， 基础路径）组成，请求路径[{0}]不能和一存在的请求路径冲突
Modules.Connection.Null=API\u0020数据库连接不能为空
Modules.Permission.Scope.Null=权限范围不能为空
Modules.Tags.Null = 所属应用不能为空

AccessCode.Is.Null=AccessCode\u0020为空
AccessCode.No.User=AccessCode\u0020找不到对应的用户

#MetadataDefinition
MetadataDefinition.Value.Exist=分类名称已存在

#reset password
Email.Not.Exist=邮箱不存在
Email.Send.fail=邮件发送失败
Email.verify.code.Send.fail=邮件发送失败，{0}
Email.Format.wrong=邮箱格式错误


#modules
Modules.Name.Existed=api名称已存在
Datasource.LinkModules=存在关联的api，不能删除


User.Not.Exist= 用户不存在
User.email.Found=找不到对应的邮箱
ValidateCode.Not.Incorrect=验证码错误
ValidateCode.Is.expired=验证码已过期
AD.Login.Fail=通过LDAP登录失败，请联系管理员
AD.Search.Fail=查询LDAP用户失败，请联系管理员
AD.Account.Not.Exists=LDAP用户在当前组不存在，请联系管理员
AD.Login.WrongPassword=通过\u0020LDAP\u0020登录失败，密码或凭据无效，请检查您的帐户是否可用且密码是否正确
AD.Login.InvalidCert=证书认证失败，请确保证书有效
AD.Login.Retryable=网络发生问题，请重试或联系管理员
Account.disabled=用户已冻结
Role.Unable.Delete=无法删除,当前角色存在绑定用户

PDK.DOWNLOAD.SOURCE.FAILED=pdk下载资源不属于当前用户

Task.CanNotSupportInspect=数据源不支持数据校验
Task.DDL.Conflict.Sync=任务中含有JS节点、自定义节点、或节点设置增量自定义SQL，暂不支持DDL，请手动关闭
Task.DDL.Conflict.Migrate=任务中含有JS节点，暂不支持DDL，请手动关闭
Task.Save.Error=任务保存失败，具体原因请查看日志信息
Agent.DesignatedAgentNotAvailable=当前对象指定的agent不可用
Task.Deleted=任务已经被删除
Email.TestSendFailed=测试发送邮件失败
Clear.Slot={0}
InvalidPaidPlan=无效的付费方案，请检查并订阅付费方案
Task.CronError = 调度表达式错误

# external storage
External.Storage.Name.Blank=名称不能为空
External.Storage.Name.Exists=名称 {0} 已经存在
External.Storage.Type.Blank=存储类型不能为空
External.Storage.Type.Invalid=非法存储类型: {0}
External.Storage.MongoDB.Uri.Blank=MongoDB连接不能为空
External.Storage.MongoDB.Database.Blank=MongoDB连接的库名不能为空
External.Storage.MongoDB.Table.Blank=MongoDB表名不能为空
External.Storage.RocksDB.Path.Blank=RocksDb存储路径不能为空
External.Storage.Cannot.Delete=无法删除,该配置正在被使用\n任务数量: {0}\n任务名称: {1}
External.Storage.ID.NULL=外存id不能为空
External.Storage.Old.Pwd.NULL=当保存外存时，替换脱敏密码失败，在旧数据中提取的密码为空
External.Storage.MongoDB.Old.Pwd.NULL=替换脱敏密码失败，在旧的MongoDB连接信息中提取的密码为空: {0}

Ldp.SourceConNotFound=源节点的数据源不存在
Ldp.FdmSyncTypeError=FDM任务的同步类型错误
Ldp.NewTaskDagNotFound=新任务缺少DAG信息
Ldp.TargetNotFound=新任务的DAG缺少目标信息
Ldp.TargetConNotFound=目标节点的数据源不存在
Ldp.MdmSyncTypeError=MDM任务的同步类型错误
Ldp.MdmTaskTargetNotMdm=目标节点的数据源信息不属于MDM配置
Ldp.RepeatTableName=表名重复，请修改表名
Ldp.TaskNotSource=新任务DAG中缺少源节点信息
Ldp.MdmTargetNoPrimaryKey=Mdm任务目标无主键或者唯一索引
Ldp.FdmSourceTableTaskExist=当前选择的表已经存在Fdm任务中
Ldp.DeleteMdmTableFailed=删除模型失败，模型不属于MDM.
Ldp.TaskTypeError=当前数据源支持的同步类型为{0}， 当前设置的类型为{1}.

CustomSql.ProcessorNodeNotJs=自定义sql过滤模式的中间处理节点只允许js节点

ApiApp.NewTagNotFound=目标应用不存在

v2_dashboard=控制台
v2_data-console=数据面板
v2_datasource_menu=连接管理
v2_data_pipeline=数据管道
v2_advanced_features=高级功能
v2_data_replication=数据复制
v2_data_flow=数据转换
v2_data_check=数据校验
v2_log_collector=共享挖掘
v2_function_management=函数管理
v2_custom_node=自定义节点
v2_shared_cache=共享缓存
v2_data_discovery=数据发现
v2_data_object=数据对象
v2_data_catalogue=数据目录
v2_data-server=数据服务
v2_api-application=应用管理
v2_data-server-list=服务管理
v2_api-client=客户端
v2_api-servers=服务器
v2_data_server_audit=服务审计
v2_api_monitor=服务监控
v2_system-management=系统管理
v2_external-storage_menu=外存管理
v2_cluster-management_menu=集群管理
v2_user_management_menu=用户管理
v2_role_management=角色管理

JsFunction.ImportFormatError=函数导入文件格式错误
JsFunction.ImportFormatError.function.name.empty=函数名不能为空
JsFunction.ImportFormatError.function.body.empty=函数体不能为空

share_cdc_task_stop_warning=该任务所使用的共享挖掘任务已停止，请尽快恢复
task_status_error=错误
task_status_stop=停止

#Type Message
ARRAY=数组
BINARY=字节数组
BOOLEAN=布尔值
DATE=日期
DATETIME=日期时间
MAP=映射
NUMBER=数值
STRING=字符串
TIME=时间
YEAR=日期（年）
UNKNOW=未知

#Log Message
AllNotices=全部通知
DeleteNotifications=删除通知
NotificationSettings=通知设置
ReadNotifications=已读通知
EnterpriseInformation=企业信息

#TapOssNonSupportFunctionException
TapOssNonSupportFunctionException=开源版本不支持该功能

#AgentGroup
group.repeat=名称已存在
group.agent.not.fund=当前Agent不存在：{0}
group.agent.repeatedly=引擎不能重复添加到相同标签
group.agent.add.failed=引擎添加标签失败，请重试
group.agent.remove.failed=引擎移除标签失败，请重试
group.response.body.failed=请求参数不能为空
group.id.empty=标签ID不能为空
group.agent.id.empty=引擎ID不能为空
group.new.name.empty=新名称不能为空
group.new.name.too.long=新名称字符长度不能大于{0}
group.not.fund=标签不存在：{0}
group.agent.not.available=当前标签暂无可用引擎关联: {0}
group.agent.not.invalid=标签ID无效
lack.group.agent=请补充Agent标签

#task import
task.import.connection.check.ConnectorNotRegister=任务导入失败，导入前请注册数据源：{0}, 依赖的版本：{1}, 如已注册请重试
task.import.connection.check.ConnectorVersionTooHeight=任务导入失败，导入任务中使用的数据源 {0} 版本过高，当前环境使用的版本：{1}, 导入任务中的版本：{2}, 请升级数据源 {3}后重新导入

relMig.parse.failed=relmig 文件解析失败, 错误原因: {0}
relMig.parse.unSupport=当前relmig文件版本{0}暂不支持，目前仅支持支持：{1}

#web hook
webhook.create.hook.defaultName=<默认Service Hook名称>
webhook.url.too.long=请求URL允许填写的最大长度是：{0}，实际填写的长度：{1}
webhook.info.mark.too.long=备注文本允许填写的最大长度是：{0}，实际填写的长度：{1}
webhook.info.custom.http.headers.too.long=自定义请求头文本允许填写的最大长度是：{0}，实际填写的长度：{1}
webhook.info.custom.template.too.long=自定义模版文本允许填写的最大长度是：{0}，实际填写的长度：{1}
webhook.info.custom.template.invalid=自定义请求体模板无效
webhook.info.custom.http.headers.invalid=自定义HTTP请求头信息无效，{0}
webhook.ping.failed=PING测试失败 {0}, 请检查您的 WebHook URL
webhook.ping.succeed=已发送测试 PING 事件到服务端，请查看发送记录确认结果
webhook.url.host.invalid=非法的IP和域名配置，不能使用当前系统的IP端口：{0}
webhook.url.invalid=参数校验失败，参数属性 service_url 格式不正确。实际值：{0}，期望类型：合法的 URI
webhook.info.existsById=WebHook配置信息不存在，WebHook ID: {0}
webhook.history.reSend=在重新发送历史 WebHook 事件消息之前，此历史事件ID不得为空
webhook.reOpen.failed=WebHook重新打开失败，请重试
webhook.history.hook.id.error=WebHook ID 不能为空
webhook.history.page.from.error=分页参数[pageFrom]无效，起始值应该大于等于0，实际值为：{0}
webhook.history.page.size.error=分页参数[pageSize]无效，每页大小应该大于0，实际值为：{0}

#auth
insufficient.permissions=权限不足: {0}，当前操作需要权限：{1}
inspect.result.not.exists=当前校验任务的校验记录已经不存在：{0}
tag.operate.not.allowed=当前用户没有权限操作该标签

source.setting.check.cdc= 增量检查】数据源:{0}的增量功能不支持，不能配置增量任务\u000d\u000a

export.inspectDetails.failed =下载校验详情失败 :{0}
read.version.file.failed=系统版本无效

openapi.generator.version.exists=版本已存在
openapi.generator.package.name.empty=包名不能为空
openapi.generator.package.name.invalid.format=包名格式无效。必须遵循Java包命名规范：小写字母、数字、下划线，用点分隔，每个段必须以字母开头
openapi.generator.package.name.contains.reserved.keyword=包名包含Java保留关键字：{0}
openapi.generator.reentry.blocked=SDK名称为：{0} 的另一个版本正在进行生成。请在开始新的生成之前等待当前的生成完成
openapi.generator.unknown.error=代码生成启动失败: {0}
openapi.generator.module.empty=请选择API并重新生成SDK

module.save.check.not-empty=输出结果中的字段列表不能为空
module.save.check.repat=输出结果中的字段别名不能重复设置：{0}
module.save.check.where=Where条件无效：{0}
module.save.check.where.tooDeep=查询嵌套太深（目前不支持超过｛0｝层的嵌套查询）
module.save.check.where.notJson=查询条件必须是对象
module.save.check.where.notOperator=无效的操作符：{0}
module.save.check.where.notAllowedOperator=不允许的操作符: {0}
module.save.check.where.isEmpty={0}：数组不能为空
module.save.check.where.notField=不允许的查询字段: {0}
module.save.check.where.notNumber={0}：值必须是数字
module.save.check.where.notNumberArray={0}：值必须是由[数字, 数字]组成的数组
module.save.check.where.notArray={0}：值必须是数组
module.save.check.where.notString={0}：值必须是字符串
module.save.check.where.notWhereString=$where：值必须是字符串或者函数
module.save.check.where.notRegex={0}：值必须是字符串或者正则表达式
module.save.check.where.notBoolean={0}：值必须是布尔值
module.save.check.where.notNumberOrString={0}：值必须是数字或者字符串
module.save.check.where.notNumberOrDate={0}：值必须是数字或者日期
module.save.check.where.notObject={0}：值必须是一个对象
module.save.check.where.notContainSearch=$text 值需要包含 $search 属性
module.save.check.where.notSearchString=$text.$search：值必须是字符串
module.save.check.where.unKnownOperator=暂不不支持操作符：{0}

api.debug.not.support=不支持的请求方法：｛0｝
api.encryption.name.empty=加密规则的名称不能为空
api.encryption.regex.empty=加密规则的正则表达式不能为空
api.encryption.name.too.long=加密规则的名称太长，最大长度为｛0｝个字符
api.encryption.regex.too.long=加密规则的正则表达式太长，最大长度为｛0｝个字符
api.encryption.description.too.long=加密规则的描述太长，最大长度为｛0｝个字符
api.encryption.name.invalid=加密规则的名称已存在：｛0}
api.encryption.id.empty=加密规则的id不能为空

#系统内置加密规则-多语言
system.phone.chinese.mainland=手机号（中国大陆）
system.phone.chinese.hk=手机号（中国香港）
system.idcard.chinese.mainland=身份证号码（中国大陆）
system.bank.card.number=银行卡号
system.chinese.mainland.passport.no.private=护照号（中国大陆）- 普通护照（因私护照）
system.chinese.mainland.passport.no.public=护照号（中国大陆）- 公务护照
system.chinese.mainland.passport.no.back=护照号（中国大陆）- 港澳回乡证
system.car.plate.number.chinese.mainland=车牌号（中国大陆）
system.chinese.name=姓名（中文）
system.birthday.date=生日日期
system.chinese.hkid=香港身份证（HKID）
system.chinese.hk.phone=香港电话（固话/手机）
system.chinese.english.name=姓名（中英文）
system.chinese.mrn.number=香港医院病历号（MRN）
system.passport.number.hongkong_uk=护照号码（香港/英国）
system.credit.card.number=信用卡号/银行卡号
system.mac.address=MAC地址
system.ipv4.address=IP地址（IPv4）
system.email.address=邮箱地址

system.description.phone.chinese.mainland=手机号（中国大陆）加密规则，保留前三位+后四位，中间替换为****
system.description.phone.chinese.hk=手机号（中国香港）加密规则，保留前两位+后两位，中间替换为****
system.description.idcard.chinese.mainland=身份证号码（中国大陆）保留前6+后4，中间替换为**********
system.description.bank.card.number=银行卡号加密规则，保留前6+后4，中间替换为***********
system.description.chinese.mainland.passport.no.private=中间四个字符替换为****
system.description.chinese.mainland.passport.no.public=中间四个字符替换为****
system.description.chinese.mainland.passport.no.back=中间四个字符替换为****
system.description.car.plate.number.chinese.mainland=车牌号（中国大陆）加密规则，保留前2位，后面替换为****
system.description.chinese.name=姓名（中文）加密规则，保留姓，名替换为*
system.description.birthday.date=生日日期加密规则，例如：2000-01-01 替换为 2**0-**-**
system.description.chinese.hkid=香港身份证（HKID）加密规则：保留首尾，中间替换为 ****（例：Z****56(7)）
system.description.chinese.hk.phone=香港电话（固话/手机）加密规则，保留前3位和后2位，中间替换为 ***
system.description.chinese.english.name=姓名（中英文）加密规则，只保留姓，名替换为 *（例：陳** / Chan ** **）
system.description.chinese.mrn.number=香港医院病历号（MRN）加密规则，保留前2位和后2位，中间替换为 ****
system.description.passport.number.hongkong_uk=护照号码（香港/英国）加密规则，保留首1位和末2位，中间替换为 *****
system.description.credit.card.number=信用卡号/银行卡号加密规则，保留前4位和后4位，中间 *
system.description.mac.address=MAC地址加密规则，部分位替换为**
system.description.ipv4.address=IP地址（IPv4）加密规则，保留前两段，后两段替换为*.*
system.description.email.address=邮箱地址，保留前2位+域名，其他替换为*

metadatainstances.compare.undo.configuration=没有可撤销的配置


api.call.metric.process.or.worker.required=API server ID 或 worker ID 二者必填其一
api.call.metric.process.id.required=API server ID 必填
api.call.metric.server.not.found=API server 不存在: {0}
api.call.api.id.required=API ID 必填
api.call.percentile.time.range.too.large=百分位数计算的时间范围不能超过7天
api.call.chart.time.range.too.large=查询范围不能超过最大查询范围：｛0｝天
api.call.chart.day.time.range.too.large=当选择以天为颗粒度进行查询时，查询范围不能超过最大查询范围：{0}天
api.call.chart.hours.time.range.too.large=当选择以小时为颗粒度进行查询时，查询范围不能超过最大查询范围：{0}小时
api.call.chart.minute.time.range.too.large=当选择以分钟为颗粒度进行查询时，查询范围不能超过最大查询范围：{0}分钟
