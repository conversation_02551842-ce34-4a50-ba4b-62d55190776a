package com.tapdata.tm.config;

import com.mongodb.MongoNamespace;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import org.bson.BsonDocument;
import org.bson.Document;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.mongodb.core.CollectionOptions;
import org.springframework.data.mongodb.core.MongoTemplate;

import java.lang.reflect.Constructor;
import java.lang.reflect.Method;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CustomMongoConfigTest {

    @Mock
    private MongoTemplate mongoTemplate;

    @Mock
    private MongoDatabase mongoDatabase;

    @Mock
    private MongoCollection<Document> mongoCollection;

    @Mock
    private CappedCollection cappedCollectionAnnotation;

    private CustomMongoConfig customMongoConfig;

    @BeforeEach
    void setUp() {
        customMongoConfig = new CustomMongoConfig(mongoTemplate);
    }

    @Test
    void testConstructor() {
        // Given & When
        CustomMongoConfig config = new CustomMongoConfig(mongoTemplate);

        // Then
        assertNotNull(config);
    }

    @Test
    void testInit_WithValidCappedCollectionClass() throws Exception {
        // Given
        String collectionName = "testCollection";
        when(mongoTemplate.collectionExists(collectionName)).thenReturn(false);
        when(cappedCollectionAnnotation.capped()).thenReturn(true);
        when(cappedCollectionAnnotation.maxLength()).thenReturn(1024L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(100L);

        // When
        customMongoConfig.init();

        // Then - init方法会扫描实际的类，这里主要验证没有异常抛出
        // 实际的测试会在具体的方法测试中进行
    }

    @Test
    void testHandleCappedCollection_CollectionNotExists() throws Exception {
        // Given
        String collectionName = "newCollection";
        when(mongoTemplate.collectionExists(collectionName)).thenReturn(false);
        when(cappedCollectionAnnotation.maxLength()).thenReturn(1024L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(100L);

        // When
        invokePrivateMethod((Object) "handleCappedCollection", collectionName, cappedCollectionAnnotation);

        // Then
        verify(mongoTemplate).createCollection(eq(collectionName), any(CollectionOptions.class));
    }

    @Test
    void testHandleCappedCollection_CollectionExists_NotCapped() throws Exception {
        // Given
        String collectionName = "existingCollection";
        when(mongoTemplate.collectionExists(collectionName)).thenReturn(true);
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        when(cappedCollectionAnnotation.maxLength()).thenReturn(1024L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(100L);

        Document collStats = new Document();
        collStats.put("capped", false);
        collStats.put("maxSize", 0L);
        collStats.put("max", 0L);
        when(mongoDatabase.runCommand(any(Document.class))).thenReturn(collStats);

        // When
        invokePrivateMethod((Object) "handleCappedCollection", collectionName, cappedCollectionAnnotation);

        // Then
        verify(mongoDatabase).runCommand(any(BsonDocument.class));
    }

    @Test
    void testHandleCappedCollection_CollectionExists_CappedWithSameAttributes() throws Exception {
        // Given
        String collectionName = "cappedCollection";
        when(mongoTemplate.collectionExists(collectionName)).thenReturn(true);
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        when(cappedCollectionAnnotation.maxLength()).thenReturn(1024L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(100L);

        Document collStats = new Document();
        collStats.put("capped", true);
        collStats.put("maxSize", 1024L);
        collStats.put("max", 100L);
        when(mongoDatabase.runCommand(any(Document.class))).thenReturn(collStats);

        // When
        invokePrivateMethod((Object) "handleCappedCollection", collectionName, cappedCollectionAnnotation);

        // Then
        verify(mongoDatabase).runCommand(any(Document.class)); // Only for stats check
        verify(mongoDatabase, never()).runCommand(any(BsonDocument.class)); // No conversion command
    }

    @Test
    void testHandleCappedCollection_CollectionExists_CappedWithDifferentAttributes() throws Exception {
        // Given
        String collectionName = "cappedCollection";
        when(mongoTemplate.collectionExists(collectionName)).thenReturn(true);
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        when(mongoDatabase.getName()).thenReturn("testDb");
        when(mongoDatabase.getCollection(collectionName)).thenReturn(mongoCollection);
        when(cappedCollectionAnnotation.maxLength()).thenReturn(2048L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(200L);

        Document collStats = new Document();
        collStats.put("capped", true);
        collStats.put("maxSize", 1024L);
        collStats.put("max", 100L);
        when(mongoDatabase.runCommand(any(Document.class))).thenReturn(collStats);

        // When
        invokePrivateMethod((Object) "handleCappedCollection", collectionName, cappedCollectionAnnotation);

        // Then
        verify(mongoCollection).renameCollection(any(MongoNamespace.class));
        verify(mongoTemplate).createCollection(anyString(), any(CollectionOptions.class));
    }

    @Test
    void testHandleCappedCollection_Exception() throws Exception {
        // Given
        String collectionName = "errorCollection";
        when(mongoTemplate.collectionExists(collectionName)).thenThrow(new RuntimeException("Database error"));

        // When & Then
        assertDoesNotThrow(() ->
            invokePrivateMethod((Object) "handleCappedCollection", collectionName, cappedCollectionAnnotation)
        );
    }

    @Test
    void testCreateCappedCollection() throws Exception {
        // Given
        String collectionName = "newCappedCollection";
        when(cappedCollectionAnnotation.maxLength()).thenReturn(1024L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(100L);

        // When
        invokePrivateMethod((Object) "createCappedCollection", collectionName, cappedCollectionAnnotation);

        // Then
        verify(mongoTemplate).createCollection(eq(collectionName), any(CollectionOptions.class));
    }

    @Test
    void testGetCollectionStats_Success() throws Exception {
        // Given
        String collectionName = "testCollection";
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);

        Document collStats = new Document();
        collStats.put("capped", true);
        collStats.put("maxSize", 1024L);
        collStats.put("max", 100L);
        when(mongoDatabase.runCommand(any(Document.class))).thenReturn(collStats);

        // When
        Object result = invokePrivateMethod((Object) "getCollectionStats", collectionName);

        // Then
        assertNotNull(result);
        verify(mongoDatabase).runCommand(any(Document.class));
    }

    @Test
    void testGetCollectionStats_Exception() throws Exception {
        // Given
        String collectionName = "errorCollection";
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        when(mongoDatabase.runCommand(any(Document.class))).thenThrow(new RuntimeException("Database error"));

        // When
        Object result = invokePrivateMethod((Object) "getCollectionStats", collectionName);

        // Then
        assertNotNull(result);
        // Should return default CollectionStats with false, 0L, 0L
    }

    @Test
    void testNeedsUpdate_True() throws Exception {
        // Given
        Object stats = createCollectionStats(true, 1024L, 100L);
        when(cappedCollectionAnnotation.maxLength()).thenReturn(2048L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(100L);

        // When
        Boolean result = (Boolean) invokePrivateMethod("needsUpdate", stats, cappedCollectionAnnotation);

        // Then
        assertTrue(result);
    }

    @Test
    void testNeedsUpdate_False() throws Exception {
        // Given
        Object stats = createCollectionStats(true, 1024L, 100L);
        when(cappedCollectionAnnotation.maxLength()).thenReturn(1024L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(100L);

        // When
        Boolean result = (Boolean) invokePrivateMethod("needsUpdate", stats, cappedCollectionAnnotation);

        // Then
        assertFalse(result);
    }

    @Test
    void testConvertToCappedCollection_Success() throws Exception {
        // Given
        String collectionName = "regularCollection";
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        when(cappedCollectionAnnotation.maxLength()).thenReturn(1024L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(100L);

        // When
        invokePrivateMethod((Object) "convertToCappedCollection", collectionName, cappedCollectionAnnotation);

        // Then
        verify(mongoDatabase).runCommand(any(BsonDocument.class));
    }

    @Test
    void testConvertToCappedCollection_WithZeroMaxMemory() throws Exception {
        // Given
        String collectionName = "regularCollection";
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        when(cappedCollectionAnnotation.maxLength()).thenReturn(1024L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(0L);

        // When
        invokePrivateMethod((Object) "convertToCappedCollection", collectionName, cappedCollectionAnnotation);

        // Then
        verify(mongoDatabase).runCommand(any(BsonDocument.class));
    }

    @Test
    void testConvertToCappedCollection_Exception() throws Exception {
        // Given
        String collectionName = "errorCollection";
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        when(mongoDatabase.runCommand(any(BsonDocument.class))).thenThrow(new RuntimeException("Conversion error"));
        when(cappedCollectionAnnotation.maxLength()).thenReturn(1024L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(100L);

        // When & Then
        assertDoesNotThrow(() ->
            invokePrivateMethod((Object) "convertToCappedCollection", collectionName, cappedCollectionAnnotation)
        );
    }

    @Test
    void testRecreateCappedCollection_Success() throws Exception {
        // Given
        String collectionName = "cappedCollection";
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        when(mongoDatabase.getName()).thenReturn("testDb");
        when(mongoDatabase.getCollection(collectionName)).thenReturn(mongoCollection);
        when(cappedCollectionAnnotation.maxLength()).thenReturn(1024L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(100L);

        // When
        invokePrivateMethod((Object) "recreateCappedCollection", collectionName, cappedCollectionAnnotation);

        // Then
        verify(mongoCollection).renameCollection(any(MongoNamespace.class));
        verify(mongoTemplate).createCollection(anyString(), any(CollectionOptions.class));
    }

    @Test
    void testRecreateCappedCollection_Exception() throws Exception {
        // Given
        String collectionName = "errorCollection";
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        when(mongoDatabase.getCollection(collectionName)).thenThrow(new RuntimeException("Rename error"));

        // When & Then
        assertDoesNotThrow(() ->
            invokePrivateMethod((Object) "recreateCappedCollection", collectionName, cappedCollectionAnnotation)
        );
    }

    @Test
    void testCollectionStats_Constructor() throws Exception {
        // Given
        boolean capped = true;
        long maxSize = 1024L;
        long max = 100L;

        // When
        Object stats = createCollectionStats(capped, maxSize, max);

        // Then
        assertNotNull(stats);
        assertEquals(capped, invokePrivateMethod(stats, "isCapped"));
        assertEquals(maxSize, invokePrivateMethod(stats, "getMaxSize"));
        assertEquals(max, invokePrivateMethod(stats, "getMax"));
    }

    // Helper methods for testing private methods and inner classes
    private Object invokePrivateMethod(String methodName, Object... args) throws Exception {
        Method method = null;
        Method[] methods = CustomMongoConfig.class.getDeclaredMethods();

        for (Method m : methods) {
            if (m.getName().equals(methodName) && m.getParameterCount() == args.length) {
                method = m;
                break;
            }
        }

        if (method == null) {
            throw new NoSuchMethodException("Method " + methodName + " not found");
        }

        method.setAccessible(true);
        return method.invoke(customMongoConfig, args);
    }

    private Object invokePrivateMethod(Object target, String methodName, Object... args) throws Exception {
        Class<?>[] paramTypes = new Class[args.length];
        for (int i = 0; i < args.length; i++) {
            paramTypes[i] = args[i].getClass();
        }

        Method method = target.getClass().getDeclaredMethod(methodName, paramTypes);
        method.setAccessible(true);
        return method.invoke(target, args);
    }

    private Object createCollectionStats(boolean capped, long maxSize, long max) throws Exception {
        // Get the inner CollectionStats class
        Class<?>[] innerClasses = CustomMongoConfig.class.getDeclaredClasses();
        Class<?> collectionStatsClass = null;
        for (Class<?> innerClass : innerClasses) {
            if (innerClass.getSimpleName().equals("CollectionStats")) {
                collectionStatsClass = innerClass;
                break;
            }
        }

        assertNotNull(collectionStatsClass, "CollectionStats inner class not found");

        // Create instance using constructor
        return collectionStatsClass.getDeclaredConstructor(boolean.class, long.class, long.class)
                .newInstance(capped, maxSize, max);
    }
}
