package com.tapdata.tm.config;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.CollectionOptions;
import com.mongodb.client.MongoDatabase;
import org.bson.Document;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CustomMongoConfigTest {

    @Mock
    private MongoTemplate mongoTemplate;

    @Mock
    private MongoDatabase mongoDatabase;

    private CustomMongoConfig customMongoConfig;

    @BeforeEach
    void setUp() {
        customMongoConfig = new CustomMongoConfig(mongoTemplate);
    }

    @Test
    void testCreateNewCappedCollection() {
        // Given
        String collectionName = "testCollection";
        when(mongoTemplate.collectionExists(collectionName)).thenReturn(false);

        // When
        customMongoConfig.init();

        // Then
        // 由于init方法会扫描所有带@CappedCollection注解的类，这里主要测试逻辑
        // 实际的集合创建会在handleCappedCollection方法中进行
        verify(mongoTemplate, atLeastOnce()).collectionExists(anyString());
    }

    @Test
    void testExistingCollectionHandling() {
        // Given
        String collectionName = "existingCollection";
        when(mongoTemplate.collectionExists(collectionName)).thenReturn(true);
        when(mongoTemplate.getMongoDbFactory().getMongoDatabase()).thenReturn(mongoDatabase);
        
        Document collStats = new Document();
        collStats.put("capped", false);
        collStats.put("maxSize", 0L);
        collStats.put("max", 0L);
        
        when(mongoDatabase.runCommand(any(Document.class))).thenReturn(collStats);

        // When
        customMongoConfig.init();

        // Then
        verify(mongoTemplate, atLeastOnce()).collectionExists(anyString());
    }
}
