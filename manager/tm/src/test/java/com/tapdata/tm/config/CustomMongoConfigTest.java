package com.tapdata.tm.config;

import com.mongodb.MongoNamespace;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import org.bson.BsonDocument;
import org.bson.Document;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.mongodb.core.CollectionOptions;
import org.springframework.data.mongodb.core.MongoTemplate;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class CustomMongoConfigTest {

    @Mock
    private MongoTemplate mongoTemplate;

    @Mock
    private MongoDatabase mongoDatabase;

    @Mock
    private MongoCollection<Document> mongoCollection;

    @Mock
    private CappedCollection cappedCollectionAnnotation;

    private CustomMongoConfig customMongoConfig;

    @BeforeEach
    void setUp() {
        customMongoConfig = new CustomMongoConfig(mongoTemplate);
    }

    @Test
    void testConstructor() {
        // Given & When
        CustomMongoConfig config = new CustomMongoConfig(mongoTemplate);
        
        // Then
        assertNotNull(config);
    }

    @Test
    void testInit() {
        // Given - init方法会扫描类路径中的@CappedCollection注解
        
        // When & Then - 验证方法执行不抛异常
        assertDoesNotThrow(() -> customMongoConfig.init());
    }

    @Test
    void testHandleCappedCollection_CollectionNotExists() {
        // Given
        String collectionName = "newCollection";
        when(mongoTemplate.collectionExists(collectionName)).thenReturn(false);
        when(cappedCollectionAnnotation.maxLength()).thenReturn(1000L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(1024L);

        // When
        customMongoConfig.handleCappedCollection(collectionName, cappedCollectionAnnotation);

        // Then
        verify(mongoTemplate).createCollection(eq(collectionName), any(CollectionOptions.class));
    }

    @Test
    void testHandleCappedCollection_CollectionExists() {
        // Given
        String collectionName = "existingCollection";
        when(mongoTemplate.collectionExists(collectionName)).thenReturn(true);
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        
        Document collStats = new Document();
        collStats.put("capped", false);
        collStats.put("maxSize", "0");
        collStats.put("max", "0");
        when(mongoDatabase.runCommand(any(Document.class))).thenReturn(collStats);
        
        when(cappedCollectionAnnotation.maxLength()).thenReturn(1000L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(1024L);

        // When
        customMongoConfig.handleCappedCollection(collectionName, cappedCollectionAnnotation);

        // Then
        verify(mongoDatabase).runCommand(any(BsonDocument.class)); // convertToCapped command
    }

    @Test
    void testHandleCappedCollection_Exception() {
        // Given
        String collectionName = "errorCollection";
        when(mongoTemplate.collectionExists(collectionName)).thenThrow(new RuntimeException("Database error"));

        // When & Then
        assertDoesNotThrow(() -> 
            customMongoConfig.handleCappedCollection(collectionName, cappedCollectionAnnotation)
        );
    }

    @Test
    void testCreateCappedCollection_WithMaxMemory() {
        // Given
        String collectionName = "newCappedCollection";
        when(cappedCollectionAnnotation.maxLength()).thenReturn(1000L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(1024L);

        // When
        customMongoConfig.createCappedCollection(collectionName, cappedCollectionAnnotation);

        // Then
        verify(mongoTemplate).createCollection(eq(collectionName), any(CollectionOptions.class));
    }

    @Test
    void testCreateCappedCollection_WithoutMaxMemory() {
        // Given
        String collectionName = "newCappedCollection";
        when(cappedCollectionAnnotation.maxLength()).thenReturn(1000L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(0L);

        // When
        customMongoConfig.createCappedCollection(collectionName, cappedCollectionAnnotation);

        // Then
        verify(mongoTemplate).createCollection(eq(collectionName), any(CollectionOptions.class));
    }

    @Test
    void testUpdateExistingCollection_NotCapped() {
        // Given
        String collectionName = "regularCollection";
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        
        Document collStats = new Document();
        collStats.put("capped", false);
        collStats.put("maxSize", "0");
        collStats.put("max", "0");
        when(mongoDatabase.runCommand(any(Document.class))).thenReturn(collStats);
        
        when(cappedCollectionAnnotation.maxLength()).thenReturn(1000L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(1024L);

        // When
        customMongoConfig.updateExistingCollection(collectionName, cappedCollectionAnnotation);

        // Then
        verify(mongoDatabase).runCommand(any(BsonDocument.class)); // convertToCapped
    }

    @Test
    void testUpdateExistingCollection_CappedSameAttributes() {
        // Given
        String collectionName = "cappedCollection";
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        
        Document collStats = new Document();
        collStats.put("capped", true);
        collStats.put("maxSize", "1000");
        collStats.put("max", "1024");
        when(mongoDatabase.runCommand(any(Document.class))).thenReturn(collStats);
        
        when(cappedCollectionAnnotation.maxLength()).thenReturn(1000L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(1024L);

        // When
        customMongoConfig.updateExistingCollection(collectionName, cappedCollectionAnnotation);

        // Then
        verify(mongoDatabase, times(1)).runCommand(any(Document.class)); // Only stats check
        verify(mongoDatabase, never()).runCommand(any(BsonDocument.class)); // No conversion
    }

    @Test
    void testUpdateExistingCollection_CappedDifferentAttributes() {
        // Given
        String collectionName = "cappedCollection";
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        when(mongoDatabase.getName()).thenReturn("testDb");
        when(mongoDatabase.getCollection(collectionName)).thenReturn(mongoCollection);
        
        Document collStats = new Document();
        collStats.put("capped", true);
        collStats.put("maxSize", "1000");
        collStats.put("max", "1024");
        when(mongoDatabase.runCommand(any(Document.class))).thenReturn(collStats);
        
        when(cappedCollectionAnnotation.maxLength()).thenReturn(2000L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(2048L);

        // When
        customMongoConfig.updateExistingCollection(collectionName, cappedCollectionAnnotation);

        // Then
        verify(mongoCollection).renameCollection(any(MongoNamespace.class));
        verify(mongoTemplate).createCollection(anyString(), any(CollectionOptions.class));
    }

    @Test
    void testUpdateExistingCollection_Exception() {
        // Given
        String collectionName = "errorCollection";
        when(mongoTemplate.getDb()).thenThrow(new RuntimeException("Database error"));

        // When & Then
        assertDoesNotThrow(() -> 
            customMongoConfig.updateExistingCollection(collectionName, cappedCollectionAnnotation)
        );
    }

    @Test
    void testGetCollectionStats_Success() {
        // Given
        String collectionName = "testCollection";
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        
        Document collStats = new Document();
        collStats.put("capped", true);
        collStats.put("maxSize", "1024");
        collStats.put("max", "100");
        when(mongoDatabase.runCommand(any(Document.class))).thenReturn(collStats);

        // When
        CustomMongoConfig.CollectionStats result = customMongoConfig.getCollectionStats(collectionName);

        // Then
        assertNotNull(result);
        assertTrue(result.isCapped());
        assertEquals(1024L, result.getMaxSize());
        assertEquals(100L, result.getMax());
    }

    @Test
    void testGetCollectionStats_Exception() {
        // Given
        String collectionName = "errorCollection";
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        when(mongoDatabase.runCommand(any(Document.class))).thenThrow(new RuntimeException("Database error"));

        // When
        CustomMongoConfig.CollectionStats result = customMongoConfig.getCollectionStats(collectionName);

        // Then
        assertNotNull(result);
        assertFalse(result.isCapped());
        assertEquals(0L, result.getMaxSize());
        assertEquals(0L, result.getMax());
    }

    @Test
    void testGetCollectionStats_NullValues() {
        // Given
        String collectionName = "testCollection";
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        
        Document collStats = new Document();
        collStats.put("capped", true);
        // maxSize and max are null
        when(mongoDatabase.runCommand(any(Document.class))).thenReturn(collStats);

        // When
        CustomMongoConfig.CollectionStats result = customMongoConfig.getCollectionStats(collectionName);

        // Then
        assertNotNull(result);
        assertTrue(result.isCapped());
        assertEquals(-1L, result.getMaxSize()); // null maxSize becomes -1L
        assertEquals(0L, result.getMax()); // null max becomes 0L
    }

    @Test
    void testNeedsUpdate_WithMaxMemory_DifferentMaxSize() {
        // Given
        CustomMongoConfig.CollectionStats stats = new CustomMongoConfig.CollectionStats(true, 1000L, 100L);
        when(cappedCollectionAnnotation.maxLength()).thenReturn(2000L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(100L);

        // When
        boolean result = customMongoConfig.needsUpdate(stats, cappedCollectionAnnotation);

        // Then
        assertTrue(result);
    }

    @Test
    void testNeedsUpdate_WithMaxMemory_DifferentMax() {
        // Given
        CustomMongoConfig.CollectionStats stats = new CustomMongoConfig.CollectionStats(true, 1000L, 100L);
        when(cappedCollectionAnnotation.maxLength()).thenReturn(1000L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(200L);

        // When
        boolean result = customMongoConfig.needsUpdate(stats, cappedCollectionAnnotation);

        // Then
        assertTrue(result);
    }

    @Test
    void testNeedsUpdate_WithMaxMemory_Same() {
        // Given
        CustomMongoConfig.CollectionStats stats = new CustomMongoConfig.CollectionStats(true, 1000L, 100L);
        when(cappedCollectionAnnotation.maxLength()).thenReturn(1000L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(100L);

        // When
        boolean result = customMongoConfig.needsUpdate(stats, cappedCollectionAnnotation);

        // Then
        assertFalse(result);
    }

    @Test
    void testNeedsUpdate_WithoutMaxMemory_DifferentMaxSize() {
        // Given
        CustomMongoConfig.CollectionStats stats = new CustomMongoConfig.CollectionStats(true, 1000L, 100L);
        when(cappedCollectionAnnotation.maxLength()).thenReturn(2000L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(0L);

        // When
        boolean result = customMongoConfig.needsUpdate(stats, cappedCollectionAnnotation);

        // Then
        assertTrue(result);
    }

    @Test
    void testNeedsUpdate_WithoutMaxMemory_Same() {
        // Given
        CustomMongoConfig.CollectionStats stats = new CustomMongoConfig.CollectionStats(true, 1000L, 100L);
        when(cappedCollectionAnnotation.maxLength()).thenReturn(1000L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(0L);

        // When
        boolean result = customMongoConfig.needsUpdate(stats, cappedCollectionAnnotation);

        // Then
        assertFalse(result);
    }

    @Test
    void testRecreateCappedCollection_Success() {
        // Given
        String collectionName = "cappedCollection";
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        when(mongoDatabase.getName()).thenReturn("testDb");
        when(mongoDatabase.getCollection(collectionName)).thenReturn(mongoCollection);
        when(cappedCollectionAnnotation.maxLength()).thenReturn(1000L);
        when(cappedCollectionAnnotation.maxMemory()).thenReturn(1024L);

        // When
        customMongoConfig.recreateCappedCollection(collectionName, cappedCollectionAnnotation);

        // Then
        verify(mongoCollection).renameCollection(any(MongoNamespace.class));
        verify(mongoTemplate).createCollection(anyString(), any(CollectionOptions.class));
    }

    @Test
    void testRecreateCappedCollection_Exception() {
        // Given
        String collectionName = "errorCollection";
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        when(mongoDatabase.getCollection(collectionName)).thenThrow(new RuntimeException("Rename error"));

        // When & Then
        assertDoesNotThrow(() ->
            customMongoConfig.recreateCappedCollection(collectionName, cappedCollectionAnnotation)
        );
    }

    @Test
    void testCollectionStats_Constructor() {
        // Given
        boolean capped = true;
        long maxSize = 1024L;
        long max = 100L;

        // When
        CustomMongoConfig.CollectionStats stats = new CustomMongoConfig.CollectionStats(capped, maxSize, max);

        // Then
        assertNotNull(stats);
        assertEquals(capped, stats.isCapped());
        assertEquals(maxSize, stats.getMaxSize());
        assertEquals(max, stats.getMax());
    }

    @Test
    void testCollectionStats_GettersWithFalseValues() {
        // Given
        boolean capped = false;
        long maxSize = 0L;
        long max = 0L;

        // When
        CustomMongoConfig.CollectionStats stats = new CustomMongoConfig.CollectionStats(capped, maxSize, max);

        // Then
        assertNotNull(stats);
        assertFalse(stats.isCapped());
        assertEquals(0L, stats.getMaxSize());
        assertEquals(0L, stats.getMax());
    }
}
