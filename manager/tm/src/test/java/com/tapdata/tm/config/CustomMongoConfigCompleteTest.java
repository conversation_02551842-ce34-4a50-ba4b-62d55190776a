package com.tapdata.tm.config;

import com.mongodb.MongoNamespace;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import org.bson.BsonDocument;
import org.bson.Document;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.mongodb.core.CollectionOptions;
import org.springframework.data.mongodb.core.MongoTemplate;

import java.lang.reflect.Constructor;
import java.lang.reflect.Method;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 完整的CustomMongoConfig测试类，目标是达到100%代码覆盖率
 */
@ExtendWith(MockitoExtension.class)
class CustomMongoConfigCompleteTest {

    @Mock
    private MongoTemplate mongoTemplate;

    @Mock
    private MongoDatabase mongoDatabase;

    @Mock
    private MongoCollection<Document> mongoCollection;

    private CustomMongoConfig customMongoConfig;

    @BeforeEach
    void setUp() {
        customMongoConfig = new CustomMongoConfig(mongoTemplate);
    }

    @Test
    void testConstructor() {
        // Given & When
        CustomMongoConfig config = new CustomMongoConfig(mongoTemplate);
        
        // Then
        assertNotNull(config);
    }

    @Test
    void testInit_NoExceptions() {
        // Given - init方法会扫描实际的类路径
        
        // When & Then - 主要验证没有异常抛出
        assertDoesNotThrow(() -> customMongoConfig.init());
    }

    @Test
    void testHandleCappedCollection_NewCollection() throws Exception {
        // Given
        String collectionName = "newCollection";
        CappedCollection annotation = createMockCappedCollection(true, 1024L, 100L);
        when(mongoTemplate.collectionExists(collectionName)).thenReturn(false);

        // When
        invokePrivateMethod("handleCappedCollection", collectionName, annotation);

        // Then
        verify(mongoTemplate).createCollection(eq(collectionName), any(CollectionOptions.class));
    }

    @Test
    void testHandleCappedCollection_ExistingNonCappedCollection() throws Exception {
        // Given
        String collectionName = "existingCollection";
        CappedCollection annotation = createMockCappedCollection(true, 1024L, 100L);
        when(mongoTemplate.collectionExists(collectionName)).thenReturn(true);
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);

        Document collStats = new Document();
        collStats.put("capped", false);
        collStats.put("maxSize", 0L);
        collStats.put("max", 0L);
        when(mongoDatabase.runCommand(any(Document.class))).thenReturn(collStats);

        // When
        invokePrivateMethod("handleCappedCollection", collectionName, annotation);

        // Then
        verify(mongoDatabase).runCommand(any(BsonDocument.class)); // convertToCapped command
    }

    @Test
    void testHandleCappedCollection_ExistingCappedSameAttributes() throws Exception {
        // Given
        String collectionName = "cappedCollection";
        CappedCollection annotation = createMockCappedCollection(true, 1024L, 100L);
        when(mongoTemplate.collectionExists(collectionName)).thenReturn(true);
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);

        Document collStats = new Document();
        collStats.put("capped", true);
        collStats.put("maxSize", 1024L);
        collStats.put("max", 100L);
        when(mongoDatabase.runCommand(any(Document.class))).thenReturn(collStats);

        // When
        invokePrivateMethod("handleCappedCollection", collectionName, annotation);

        // Then
        verify(mongoDatabase, times(1)).runCommand(any(Document.class)); // Only stats check
        verify(mongoDatabase, never()).runCommand(any(BsonDocument.class)); // No conversion
    }

    @Test
    void testHandleCappedCollection_ExistingCappedDifferentAttributes() throws Exception {
        // Given
        String collectionName = "cappedCollection";
        CappedCollection annotation = createMockCappedCollection(true, 2048L, 200L);
        when(mongoTemplate.collectionExists(collectionName)).thenReturn(true);
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        when(mongoDatabase.getName()).thenReturn("testDb");
        when(mongoDatabase.getCollection(collectionName)).thenReturn(mongoCollection);

        Document collStats = new Document();
        collStats.put("capped", true);
        collStats.put("maxSize", 1024L);
        collStats.put("max", 100L);
        when(mongoDatabase.runCommand(any(Document.class))).thenReturn(collStats);

        // When
        invokePrivateMethod("handleCappedCollection", collectionName, annotation);

        // Then
        verify(mongoCollection).renameCollection(any(MongoNamespace.class));
        verify(mongoTemplate).createCollection(anyString(), any(CollectionOptions.class));
    }

    @Test
    void testHandleCappedCollection_Exception() throws Exception {
        // Given
        String collectionName = "errorCollection";
        CappedCollection annotation = createMockCappedCollection(true, 1024L, 100L);
        when(mongoTemplate.collectionExists(collectionName)).thenThrow(new RuntimeException("Database error"));

        // When & Then
        assertDoesNotThrow(() -> 
            invokePrivateMethod("handleCappedCollection", collectionName, annotation)
        );
    }

    @Test
    void testCreateCappedCollection() throws Exception {
        // Given
        String collectionName = "newCappedCollection";
        CappedCollection annotation = createMockCappedCollection(true, 1024L, 100L);

        // When
        invokePrivateMethod("createCappedCollection", collectionName, annotation);

        // Then
        verify(mongoTemplate).createCollection(eq(collectionName), any(CollectionOptions.class));
    }

    @Test
    void testGetCollectionStats_Success() throws Exception {
        // Given
        String collectionName = "testCollection";
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        
        Document collStats = new Document();
        collStats.put("capped", true);
        collStats.put("maxSize", 1024L);
        collStats.put("max", 100L);
        when(mongoDatabase.runCommand(any(Document.class))).thenReturn(collStats);

        // When
        Object result = invokePrivateMethod("getCollectionStats", collectionName);

        // Then
        assertNotNull(result);
        verify(mongoDatabase).runCommand(any(Document.class));
    }

    @Test
    void testGetCollectionStats_Exception() throws Exception {
        // Given
        String collectionName = "errorCollection";
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        when(mongoDatabase.runCommand(any(Document.class))).thenThrow(new RuntimeException("Database error"));

        // When
        Object result = invokePrivateMethod("getCollectionStats", collectionName);

        // Then
        assertNotNull(result);
        // Should return default CollectionStats with false, 0L, 0L
    }

    @Test
    void testConvertToCappedCollection_WithMaxMemory() throws Exception {
        // Given
        String collectionName = "regularCollection";
        CappedCollection annotation = createMockCappedCollection(true, 1024L, 100L);
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);

        // When
        invokePrivateMethod("convertToCappedCollection", collectionName, annotation);

        // Then
        verify(mongoDatabase).runCommand(any(BsonDocument.class));
    }

    @Test
    void testConvertToCappedCollection_WithoutMaxMemory() throws Exception {
        // Given
        String collectionName = "regularCollection";
        CappedCollection annotation = createMockCappedCollection(true, 1024L, 0L);
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);

        // When
        invokePrivateMethod("convertToCappedCollection", collectionName, annotation);

        // Then
        verify(mongoDatabase).runCommand(any(BsonDocument.class));
    }

    @Test
    void testConvertToCappedCollection_Exception() throws Exception {
        // Given
        String collectionName = "errorCollection";
        CappedCollection annotation = createMockCappedCollection(true, 1024L, 100L);
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        when(mongoDatabase.runCommand(any(BsonDocument.class))).thenThrow(new RuntimeException("Conversion error"));

        // When & Then
        assertDoesNotThrow(() -> 
            invokePrivateMethod("convertToCappedCollection", collectionName, annotation)
        );
    }

    @Test
    void testRecreateCappedCollection_Success() throws Exception {
        // Given
        String collectionName = "cappedCollection";
        CappedCollection annotation = createMockCappedCollection(true, 1024L, 100L);
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        when(mongoDatabase.getName()).thenReturn("testDb");
        when(mongoDatabase.getCollection(collectionName)).thenReturn(mongoCollection);

        // When
        invokePrivateMethod("recreateCappedCollection", collectionName, annotation);

        // Then
        verify(mongoCollection).renameCollection(any(MongoNamespace.class));
        verify(mongoTemplate).createCollection(anyString(), any(CollectionOptions.class));
    }

    @Test
    void testRecreateCappedCollection_Exception() throws Exception {
        // Given
        String collectionName = "errorCollection";
        CappedCollection annotation = createMockCappedCollection(true, 1024L, 100L);
        when(mongoTemplate.getDb()).thenReturn(mongoDatabase);
        when(mongoDatabase.getCollection(collectionName)).thenThrow(new RuntimeException("Rename error"));

        // When & Then
        assertDoesNotThrow(() -> 
            invokePrivateMethod("recreateCappedCollection", collectionName, annotation)
        );
    }

    @Test
    void testCollectionStats_AllMethods() throws Exception {
        // Given
        Object stats = createCollectionStats(true, 1024L, 100L);

        // When & Then
        assertTrue((Boolean) invokeMethod(stats, "isCapped"));
        assertEquals(1024L, (Long) invokeMethod(stats, "getMaxSize"));
        assertEquals(100L, (Long) invokeMethod(stats, "getMax"));
    }

    @Test
    void testNeedsUpdate_DifferentMaxSize() throws Exception {
        // Given
        Object stats = createCollectionStats(true, 1024L, 100L);
        CappedCollection annotation = createMockCappedCollection(true, 2048L, 100L);

        // When
        Boolean result = (Boolean) invokePrivateMethod("needsUpdate", stats, annotation);

        // Then
        assertTrue(result);
    }

    @Test
    void testNeedsUpdate_DifferentMaxMemory() throws Exception {
        // Given
        Object stats = createCollectionStats(true, 1024L, 100L);
        CappedCollection annotation = createMockCappedCollection(true, 1024L, 200L);

        // When
        Boolean result = (Boolean) invokePrivateMethod("needsUpdate", stats, annotation);

        // Then
        assertTrue(result);
    }

    @Test
    void testNeedsUpdate_SameAttributes() throws Exception {
        // Given
        Object stats = createCollectionStats(true, 1024L, 100L);
        CappedCollection annotation = createMockCappedCollection(true, 1024L, 100L);

        // When
        Boolean result = (Boolean) invokePrivateMethod("needsUpdate", stats, annotation);

        // Then
        assertFalse(result);
    }

    // Helper methods
    private CappedCollection createMockCappedCollection(boolean capped, long maxLength, long maxMemory) {
        CappedCollection annotation = mock(CappedCollection.class);
        when(annotation.capped()).thenReturn(capped);
        when(annotation.maxLength()).thenReturn(maxLength);
        when(annotation.maxMemory()).thenReturn(maxMemory);
        return annotation;
    }

    private Object invokePrivateMethod(String methodName, Object... args) throws Exception {
        Method method = null;
        Method[] methods = CustomMongoConfig.class.getDeclaredMethods();
        
        for (Method m : methods) {
            if (m.getName().equals(methodName) && m.getParameterCount() == args.length) {
                method = m;
                break;
            }
        }
        
        if (method == null) {
            throw new NoSuchMethodException("Method " + methodName + " not found");
        }
        
        method.setAccessible(true);
        return method.invoke(customMongoConfig, args);
    }

    private Object invokeMethod(Object target, String methodName, Object... args) throws Exception {
        Method method = target.getClass().getDeclaredMethod(methodName, new Class[0]);
        method.setAccessible(true);
        return method.invoke(target, args);
    }

    private Object createCollectionStats(boolean capped, long maxSize, long max) throws Exception {
        Class<?>[] innerClasses = CustomMongoConfig.class.getDeclaredClasses();
        Class<?> collectionStatsClass = null;
        for (Class<?> innerClass : innerClasses) {
            if (innerClass.getSimpleName().equals("CollectionStats")) {
                collectionStatsClass = innerClass;
                break;
            }
        }
        
        assertNotNull(collectionStatsClass, "CollectionStats inner class not found");
        
        Constructor<?> constructor = collectionStatsClass.getDeclaredConstructor(boolean.class, long.class, long.class);
        constructor.setAccessible(true);
        return constructor.newInstance(capped, maxSize, max);
    }
}
