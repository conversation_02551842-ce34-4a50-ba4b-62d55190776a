package com.tapdata.tm.config;

import com.mongodb.client.MongoDatabase;
import lombok.Getter;
import org.bson.BsonDocument;
import org.bson.BsonInt64;
import org.bson.BsonString;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.data.mongodb.core.CollectionOptions;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.mapping.Document;

import javax.annotation.PostConstruct;
import java.util.Optional;

@Configuration
public class CustomMongoConfig {

    private static final Logger logger = LoggerFactory.getLogger(CustomMongoConfig.class);

    private final MongoTemplate mongoTemplate;

    public CustomMongoConfig(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @PostConstruct
    public void init() {
        ClassPathScanningCandidateComponentProvider provider =
                new ClassPathScanningCandidateComponentProvider(false);
        provider.addIncludeFilter(new AnnotationTypeFilter(CappedCollection.class));
        provider.findCandidateComponents("com.tapdata.tm")
                .forEach(beanDef -> {
                    try {
                        Class<?> clazz = Class.forName(beanDef.getBeanClassName());
                        Document entityAnnotation = clazz.getAnnotation(Document.class);
                        if (null == entityAnnotation) {
                            return;
                        }
                        CappedCollection annotation = clazz.getAnnotation(CappedCollection.class);
                        if (null == annotation) {
                            return;
                        }
                        String name = entityAnnotation.value();
                        if (annotation.capped()) {
                            handleCappedCollection(name, annotation);
                        }
                    } catch (Exception e) {
                        logger.warn("Failed to process capped collection for class: {}, error: {}",
                                beanDef.getBeanClassName(), e.getMessage());
                    }
                });
    }

    private void handleCappedCollection(String collectionName, CappedCollection annotation) {
        try {
            if (!mongoTemplate.collectionExists(collectionName)) {
                createCappedCollection(collectionName, annotation);
            } else {
                updateExistingCollection(collectionName, annotation);
            }
        } catch (Exception e) {
            logger.error("Failed to handle capped collection: {}, error: {}", collectionName, e.getMessage(), e);
        }
    }

    private void createCappedCollection(String collectionName, CappedCollection annotation) {
        logger.info("Creating new capped collection: {}", collectionName);
        CollectionOptions collectionOptions = CollectionOptions.empty()
                .capped()
                .maxDocuments(annotation.maxLength());
        if (annotation.maxMemory() > 0L) {
            collectionOptions.size(annotation.maxMemory());
        }
        mongoTemplate.createCollection(collectionName, collectionOptions);
        logger.info("Successfully created capped collection: {}", collectionName);
    }

    private void updateExistingCollection(String collectionName, CappedCollection annotation) {
        try {
            CollectionStats stats = getCollectionStats(collectionName);
            if (!stats.isCapped()) {
                convertToCappedCollection(collectionName, annotation);
            } else {
                if (needsUpdate(stats, annotation)) {
                    logger.info("Capped collection {} needs attribute update", collectionName);
                    recreateCappedCollection(collectionName, annotation);
                } else {
                    logger.debug("Capped collection {} attributes are up to date", collectionName);
                }
            }
        } catch (Exception e) {
            logger.error("Failed to update existing collection: {}, error: {}", collectionName, e.getMessage(), e);
        }
    }

    private CollectionStats getCollectionStats(String collectionName) {
        try {
            MongoDatabase database = mongoTemplate.getDb();
            org.bson.Document collStats = database.runCommand(
                    new org.bson.Document("collStats", collectionName)
            );

            boolean capped = collStats.getBoolean("capped", false);
            long maxSize = Optional.ofNullable(collStats.get("maxSize"))
                    .map(String::valueOf)
                    .map(Long::parseLong)
                    .orElse(-1L);
            long max = Optional.ofNullable(collStats.get("max"))
                    .map(String::valueOf)
                    .map(Long::parseLong)
                    .orElse(0L);

            return new CollectionStats(capped, maxSize, max);
        } catch (Exception e) {
            logger.error("Failed to get collection stats for: {}, error: {}", collectionName, e.getMessage());
            return new CollectionStats(false, 0L, 0L);
        }
    }

    private boolean needsUpdate(CollectionStats stats, CappedCollection annotation) {
        if (annotation.maxMemory() > 0L) {
            return stats.getMaxSize() != annotation.maxLength() ||
                    stats.getMax() != annotation.maxMemory();
        }
        return stats.getMaxSize() != annotation.maxLength();
    }

    private void convertToCappedCollection(String collectionName, CappedCollection annotation) {
        try {
            logger.info("Converting collection {} to capped collection", collectionName);

            MongoDatabase database = mongoTemplate.getDb();
            BsonDocument command = new BsonDocument();
            command.put("convertToCapped", new BsonString(collectionName));
            command.put("size", new BsonInt64(annotation.maxLength()));
            if (annotation.maxMemory() > 0L) {
                command.put("max", new BsonInt64(annotation.maxMemory()));
            }
            database.runCommand(command);
            logger.info("Successfully converted collection {} to capped", collectionName);
        } catch (Exception e) {
            logger.error("Failed to convert collection {} to capped: {}", collectionName, e.getMessage(), e);
        }
    }

    /**
     * 重建capped集合（当需要修改capped属性时）
     * 注意：这个操作会导致数据丢失，因为MongoDB不支持直接修改capped集合的属性
     */
    private void recreateCappedCollection(String collectionName, CappedCollection annotation) {
        try {
            logger.warn("Recreating capped collection {} with new attributes. This will cause data loss!", collectionName);
            String backupCollectionName = collectionName + "_backup_" + System.currentTimeMillis();

            MongoDatabase database = mongoTemplate.getDb();
            database.getCollection(collectionName).renameCollection(
                    new com.mongodb.MongoNamespace(database.getName(), backupCollectionName)
            );
            createCappedCollection(collectionName, annotation);
            logger.info("Successfully recreated capped collection {}. Backup saved as {}",
                    collectionName, backupCollectionName);
            logger.warn("Please manually handle the backup collection: {}", backupCollectionName);
        } catch (Exception e) {
            logger.error("Failed to recreate capped collection {}: {}", collectionName, e.getMessage(), e);
        }
    }


    @Getter
    private static class CollectionStats {
        private final boolean capped;
        private final long maxSize;
        private final long max;

        public CollectionStats(boolean capped, long maxSize, long max) {
            this.capped = capped;
            this.maxSize = maxSize;
            this.max = max;
        }
    }
}
