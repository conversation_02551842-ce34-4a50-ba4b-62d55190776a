package com.tapdata.tm.config;

import org.springframework.context.annotation.ClassPathScanningCandidateComponentProvider;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.type.filter.AnnotationTypeFilter;
import org.springframework.data.mongodb.core.CollectionOptions;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.mapping.Document;
import com.mongodb.client.MongoDatabase;
import org.bson.BsonDocument;
import org.bson.BsonString;
import org.bson.BsonInt64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.PostConstruct;

@Configuration
public class CustomMongoConfig {

    private static final Logger logger = LoggerFactory.getLogger(CustomMongoConfig.class);

    private final MongoTemplate mongoTemplate;

    public CustomMongoConfig(MongoTemplate mongoTemplate) {
        this.mongoTemplate = mongoTemplate;
    }

    @PostConstruct
    public void init() {
        ClassPathScanningCandidateComponentProvider provider =
                new ClassPathScanningCandidateComponentProvider(false);
        provider.addIncludeFilter(new AnnotationTypeFilter(CappedCollection.class));
        provider.findCandidateComponents("com.tapdata.tm")
                .forEach(beanDef -> {
                    try {
                        Class<?> clazz = Class.forName(beanDef.getBeanClassName());
                        Document entityAnnotation = clazz.getAnnotation(Document.class);
                        if (null == entityAnnotation) {
                            return;
                        }
                        CappedCollection annotation = clazz.getAnnotation(CappedCollection.class);
                        if (null == annotation) {
                            return;
                        }
                        String name = entityAnnotation.value();
                        if (annotation.capped()) {
                            handleCappedCollection(name, annotation);
                        }
                    } catch (Exception e) {
                        logger.warn("Failed to process capped collection for class: {}, error: {}",
                                beanDef.getBeanClassName(), e.getMessage());
                    }
                });
    }

    /**
     * 处理capped集合的创建和更新
     */
    private void handleCappedCollection(String collectionName, CappedCollection annotation) {
        try {
            if (!mongoTemplate.collectionExists(collectionName)) {
                // 集合不存在，创建新的capped集合
                createCappedCollection(collectionName, annotation);
            } else {
                // 集合已存在，检查并更新capped属性
                updateExistingCollection(collectionName, annotation);
            }
        } catch (Exception e) {
            logger.error("Failed to handle capped collection: {}, error: {}", collectionName, e.getMessage(), e);
        }
    }

    /**
     * 创建新的capped集合
     */
    private void createCappedCollection(String collectionName, CappedCollection annotation) {
        logger.info("Creating new capped collection: {}", collectionName);
        mongoTemplate.createCollection(collectionName,
                CollectionOptions.empty()
                        .capped()
                        .size(annotation.maxLength())
                        .maxDocuments(annotation.maxMemory())
        );
        logger.info("Successfully created capped collection: {}", collectionName);
    }

    /**
     * 更新已存在的集合为capped集合或更新capped属性
     */
    private void updateExistingCollection(String collectionName, CappedCollection annotation) {
        try {
            // 获取集合状态
            CollectionStats stats = getCollectionStats(collectionName);

            if (!stats.isCapped()) {
                // 集合不是capped，转换为capped集合
                convertToCappedCollection(collectionName, annotation);
            } else {
                // 集合已经是capped，检查属性是否需要更新
                if (needsUpdate(stats, annotation)) {
                    logger.info("Capped collection {} needs attribute update", collectionName);
                    recreateCappedCollection(collectionName, annotation);
                } else {
                    logger.debug("Capped collection {} attributes are up to date", collectionName);
                }
            }
        } catch (Exception e) {
            logger.error("Failed to update existing collection: {}, error: {}", collectionName, e.getMessage(), e);
        }
    }

    /**
     * 获取集合统计信息
     */
    private CollectionStats getCollectionStats(String collectionName) {
        try {
            MongoDatabase database = mongoTemplate.getDb();
            org.bson.Document collStats = database.runCommand(
                    new org.bson.Document("collStats", collectionName)
            );

            boolean capped = collStats.getBoolean("capped", false);
            long maxSize = collStats.getLong("maxSize", 0L);
            long max = collStats.getLong("max", 0L);

            return new CollectionStats(capped, maxSize, max);
        } catch (Exception e) {
            logger.error("Failed to get collection stats for: {}, error: {}", collectionName, e.getMessage());
            return new CollectionStats(false, 0L, 0L);
        }
    }

    /**
     * 检查capped集合属性是否需要更新
     */
    private boolean needsUpdate(CollectionStats stats, CappedCollection annotation) {
        return stats.getMaxSize() != annotation.maxLength() ||
               stats.getMax() != annotation.maxMemory();
    }

    /**
     * 将普通集合转换为capped集合
     */
    private void convertToCappedCollection(String collectionName, CappedCollection annotation) {
        try {
            logger.info("Converting collection {} to capped collection", collectionName);

            MongoDatabase database = mongoTemplate.getMongoDbFactory().getMongoDatabase();
            BsonDocument command = new BsonDocument();
            command.put("convertToCapped", new BsonString(collectionName));
            command.put("size", new BsonInt64(annotation.maxLength()));
            if (annotation.maxMemory() > 0) {
                command.put("max", new BsonInt64(annotation.maxMemory()));
            }

            database.runCommand(command);
            logger.info("Successfully converted collection {} to capped", collectionName);
        } catch (Exception e) {
            logger.error("Failed to convert collection {} to capped: {}", collectionName, e.getMessage(), e);
        }
    }

    /**
     * 重建capped集合（当需要修改capped属性时）
     * 注意：这个操作会导致数据丢失，因为MongoDB不支持直接修改capped集合的属性
     */
    private void recreateCappedCollection(String collectionName, CappedCollection annotation) {
        try {
            logger.warn("Recreating capped collection {} with new attributes. This will cause data loss!", collectionName);

            // 备份集合名
            String backupCollectionName = collectionName + "_backup_" + System.currentTimeMillis();

            // 重命名原集合为备份集合
            MongoDatabase database = mongoTemplate.getMongoDbFactory().getMongoDatabase();
            database.getCollection(collectionName).renameCollection(
                    new com.mongodb.MongoNamespace(database.getName(), backupCollectionName)
            );

            // 创建新的capped集合
            createCappedCollection(collectionName, annotation);

            // 可选：从备份集合恢复部分数据（受capped集合大小限制）
            // 这里可以根据需要实现数据迁移逻辑

            logger.info("Successfully recreated capped collection {}. Backup saved as {}",
                    collectionName, backupCollectionName);
            logger.warn("Please manually handle the backup collection: {}", backupCollectionName);

        } catch (Exception e) {
            logger.error("Failed to recreate capped collection {}: {}", collectionName, e.getMessage(), e);
        }
    }

    /**
     * 集合统计信息内部类
     */
    private static class CollectionStats {
        private final boolean capped;
        private final long maxSize;
        private final long max;

        public CollectionStats(boolean capped, long maxSize, long max) {
            this.capped = capped;
            this.maxSize = maxSize;
            this.max = max;
        }

        public boolean isCapped() {
            return capped;
        }

        public long getMaxSize() {
            return maxSize;
        }

        public long getMax() {
            return max;
        }
    }
}
